'use strict';function aa(){return function(a){return a}}function k(){return function(){}}function p(a){return function(){return this[a]}}function ba(a){return function(){return a}}var q;function ca(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var fa=ea(this);function r(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&da(c,a,{configurable:!0,writable:!0,value:b})}}
r("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;da(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=p("g");var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});
r("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=fa[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&da(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ca(this))}})}return a});function ha(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
var ia=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
function u(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.ba=b.prototype}function w(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}
function oa(a){if(!(a instanceof Array)){a=w(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function pa(a){return qa(a,a)}function qa(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a}function ra(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}r("globalThis",function(a){return a||fa});
r("Promise",function(a){function b(g){this.g=0;this.l=void 0;this.j=[];this.G=!1;var h=this.o();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.j=function(g){if(this.g==null){this.g=[];var h=this;this.l(function(){h.B()})}this.g.push(g)};var e=fa.setTimeout;c.prototype.l=function(g){e(g,0)};c.prototype.B=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var l=
g[h];g[h]=null;try{l()}catch(m){this.o(m)}}}this.g=null};c.prototype.o=function(g){this.l(function(){throw g;})};b.prototype.o=function(){function g(m){return function(n){l||(l=!0,m.call(h,n))}}var h=this,l=!1;return{resolve:g(this.N),reject:g(this.B)}};b.prototype.N=function(g){if(g===this)this.B(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.P(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.L(g):this.A(g)}};
b.prototype.L=function(g){var h=void 0;try{h=g.then}catch(l){this.B(l);return}typeof h=="function"?this.T(h,g):this.A(g)};b.prototype.B=function(g){this.C(2,g)};b.prototype.A=function(g){this.C(1,g)};b.prototype.C=function(g,h){if(this.g!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.l=h;this.g===2&&this.U();this.H()};b.prototype.U=function(){var g=this;e(function(){if(g.J()){var h=fa.console;typeof h!=="undefined"&&h.error(g.l)}},1)};b.prototype.J=
function(){if(this.G)return!1;var g=fa.CustomEvent,h=fa.Event,l=fa.dispatchEvent;if(typeof l==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=fa.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.l;return l(g)};b.prototype.H=function(){if(this.j!=null){for(var g=0;g<this.j.length;++g)f.j(this.j[g]);this.j=null}};var f=new c;
b.prototype.P=function(g){var h=this.o();g.ta(h.resolve,h.reject)};b.prototype.T=function(g,h){var l=this.o();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};b.prototype.then=function(g,h){function l(x,v){return typeof x=="function"?function(N){try{m(x(N))}catch(Q){n(Q)}}:v}var m,n,t=new b(function(x,v){m=x;n=v});this.ta(l(g,m),l(h,n));return t};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.ta=function(g,h){function l(){switch(m.g){case 1:g(m.l);break;case 2:h(m.l);
break;default:throw Error("Unexpected state: "+m.g);}}var m=this;this.j==null?f.j(l):this.j.push(l);this.G=!0};b.resolve=d;b.reject=function(g){return new b(function(h,l){l(g)})};b.race=function(g){return new b(function(h,l){for(var m=w(g),n=m.next();!n.done;n=m.next())d(n.value).ta(h,l)})};b.all=function(g){var h=w(g),l=h.next();return l.done?d([]):new b(function(m,n){function t(N){return function(Q){x[N]=Q;v--;v==0&&m(x)}}var x=[],v=0;do x.push(void 0),v++,d(l.value).ta(t(x.length-1),n),l=h.next();
while(!l.done)})};return b});function sa(a,b){return Object.prototype.hasOwnProperty.call(a,b)}r("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});r("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
r("WeakMap",function(a){function b(l){this.g=(h+=Math.random()+1).toString();if(l){l=w(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function e(l){if(!sa(l,g)){var m=new c;da(l,g,{value:m})}}function f(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),
n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(t){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(l,m){if(!d(l))throw Error("Invalid WeakMap key");e(l);if(!sa(l,g))throw Error("WeakMap key fail: "+l);l[g][this.g]=m;return this};b.prototype.get=function(l){return d(l)&&sa(l,g)?l[g][this.g]:void 0};b.prototype.has=function(l){return d(l)&&sa(l,
g)&&sa(l[g],this.g)};b.prototype.delete=function(l){return d(l)&&sa(l,g)&&sa(l[g],this.g)?delete l[g][this.g]:!1};return b});
r("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,l){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.previous;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})}function d(h,l){var m=l&&typeof l;m=="object"||m=="function"?f.has(l)?m=f.get(l):(m=""+ ++g,f.set(l,m)):m="p_"+l;var n=h[0][m];if(n&&sa(h[0],m))for(h=0;h<n.length;h++){var t=n[h];if(l!==l&&t.key!==t.key||l===t.key)return{id:m,list:n,index:h,entry:t}}return{id:m,
list:n,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=w(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),l=new a(w([[h,"s"]]));if(l.get(h)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=
4||n.value[1]!="t"||!m.next().done?!1:!0}catch(t){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,l){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:l},m.list.push(m.entry),this[1].previous.next=m.entry,this[1].previous=m.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,h.call(l,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});
r("Set",function(a){function b(c){this.g=new Map;if(c){c=w(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(w([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});r("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push(b[d]);return c}});r("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
r("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});function ta(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}
r("String.prototype.includes",function(a){return a?a:function(b,c){return ta(this,b,"includes").indexOf(b,c||0)!==-1}});r("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:aa();var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
r("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push([d,b[d]]);return c}});r("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});r("Number.MAX_SAFE_INTEGER",ba(9007199254740991));r("Number.MIN_SAFE_INTEGER",ba(-9007199254740991));r("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
r("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});r("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ta(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
function ua(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}r("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}});r("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
r("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});r("Array.prototype.keys",function(a){return a?a:function(){return ua(this,aa())}});r("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}});r("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
function va(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]}r("Array.prototype.at",function(a){return a?a:va});function wa(a){return a?a:va}r("Int8Array.prototype.at",wa);r("Uint8Array.prototype.at",wa);r("Uint8ClampedArray.prototype.at",wa);r("Int16Array.prototype.at",wa);r("Uint16Array.prototype.at",wa);r("Int32Array.prototype.at",wa);r("Uint32Array.prototype.at",wa);r("Float32Array.prototype.at",wa);r("Float64Array.prototype.at",wa);
r("String.prototype.at",function(a){return a?a:va});
r("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,f={next:function(){if(e)return{value:void 0,done:!0};var g=c.exec(d);if(!g)return e=!0,{value:void 0,done:!0};g[0]===""&&(c.lastIndex+=1);return{value:g,done:!1}}};f[Symbol.iterator]=function(){return f};return f}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var xa=xa||{},y=this||self;function ya(a,b){var c=za("CLOSURE_FLAGS");a=c&&c[a];return a!=null?a:b}function za(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function Aa(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function Ba(a){var b=Aa(a);return b=="array"||b=="object"&&typeof a.length=="number"}function Ca(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}var Da="closure_uid_"+(Math.random()*1E9>>>0),Ea=0;
function Fa(a,b,c){return a.call.apply(a.bind,arguments)}function Ga(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Ha(a,b,c){Ha=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Fa:Ga;return Ha.apply(null,arguments)}
function Ia(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function Ja(a){(0,eval)(a)}function Ka(a){return a}function z(a,b){function c(){}c.prototype=b.prototype;a.ba=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ac=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function La(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,La);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.g=!0}z(La,Error);La.prototype.name="CustomError";function Ma(a){y.setTimeout(function(){throw a;},0)};var Na=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var Oa=ya(610401301,!1),Pa=ya(748402147,ya(1,!1));function Qa(){var a=y.navigator;return a&&(a=a.userAgent)?a:""}var Ra,Sa=y.navigator;Ra=Sa?Sa.userAgentData||null:null;function Ta(a){if(!Oa||!Ra)return!1;for(var b=0;b<Ra.brands.length;b++){var c=Ra.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function A(a){return Qa().indexOf(a)!=-1};function Ua(){return Oa?!!Ra&&Ra.brands.length>0:!1}function Va(){return A("Firefox")||A("FxiOS")}function Ya(){return Ua()?Ta("Chromium"):(A("Chrome")||A("CriOS"))&&!(Ua()?0:A("Edge"))||A("Silk")};function Za(){return Oa?!!Ra&&!!Ra.platform:!1}function $a(){return A("iPhone")&&!A("iPod")&&!A("iPad")}function ab(){$a()||A("iPad")||A("iPod")};function bb(a,b){return Array.prototype.indexOf.call(a,b,void 0)}function cb(a,b){return Array.prototype.some.call(a,b,void 0)}function db(a,b){b=bb(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function eb(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(Ba(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};A("Mobile");Za()||A("Macintosh");Za()||A("Windows");(Za()?Ra.platform==="Linux":A("Linux"))||Za()||A("CrOS");Za()||A("Android");$a();A("iPad");A("iPod");ab();Qa().toLowerCase().indexOf("kaios");Va();$a()||A("iPod");A("iPad");!A("Android")||Ya()||Va()||(Ua()?0:A("Opera"))||A("Silk");Ya();!A("Safari")||Ya()||(Ua()?0:A("Coast"))||(Ua()?0:A("Opera"))||(Ua()?0:A("Edge"))||(Ua()?Ta("Microsoft Edge"):A("Edg/"))||(Ua()?Ta("Opera"):A("OPR"))||Va()||A("Silk")||A("Android")||ab();var fb={},gb=null;var hb=typeof Uint8Array!=="undefined",ib=typeof btoa==="function",jb={},kb=typeof structuredClone!="undefined";function lb(a,b){if(b!==jb)throw Error("illegal external caller");this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}function mb(){return nb||(nb=new lb(null,jb))}var nb;function ob(a,b,c){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382[b]=c}function pb(a){return a.__closure__error__context__984382||{}};var qb=void 0;function rb(a,b){if(a!=null){var c;var d=(c=qb)!=null?c:qb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),ob(a,"severity","incident"),Ma(a))}};var sb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function tb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var vb=tb("jas",void 0,!0),wb=tb(void 0,"0di"),xb=tb(void 0,"1oa"),yb=tb(void 0,Symbol()),zb=tb(void 0,"0ubs"),Ab=tb(void 0,"0actk"),Bb=tb("m_m","Dc",!0);Math.max.apply(Math,oa(Object.values({ec:1,cc:2,Zb:4,oc:8,xc:16,kc:32,Mb:64,Xb:128,Vb:256,uc:512,Wb:1024,Yb:2048,lc:4096})));var Cb={yb:{value:0,configurable:!0,writable:!0,enumerable:!1}},Db=Object.defineProperties,B=sb?vb:"yb",Eb,Fb=[];Gb(Fb,7);Eb=Object.freeze(Fb);function Hb(a,b){sb||B in a||Db(a,Cb);a[B]|=b}function Gb(a,b){sb||B in a||Db(a,Cb);a[B]=b}function Ib(a){Hb(a,34);return a};function Jb(){return typeof BigInt==="function"};var Kb={};function Lb(a,b){return b===void 0?a.g!==Mb&&!!(2&(a.D[B]|0)):!!(2&b)&&a.g!==Mb}var Mb={},Nb=Object.freeze({});function Ob(a){a.Cc=!0;return a};var Pb=Ob(function(a){return typeof a==="number"}),Qb=Ob(function(a){return typeof a==="string"}),Rb=Ob(function(a){return typeof a==="boolean"}),Sb=Ob(function(a){return typeof a==="bigint"});var Tb=typeof y.BigInt==="function"&&typeof y.BigInt(0)==="bigint";function Ub(a){var b=a;if(Qb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Pb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Tb?BigInt(a):a=Rb(a)?a?"1":"0":Qb(a)?a.trim()||"0":String(a)}
var Vb=Ob(function(a){return Tb?Sb(a):Qb(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)}),bc=Ob(function(a){return Tb?a>=Xb&&a<=Yb:a[0]==="-"?Zb(a,$b):Zb(a,ac)}),$b=Number.MIN_SAFE_INTEGER.toString(),Xb=Tb?BigInt(Number.MIN_SAFE_INTEGER):void 0,ac=Number.MAX_SAFE_INTEGER.toString(),Yb=Tb?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Zb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var C=0,cc=0;function dc(a){var b=a>>>0;C=b;cc=(a-b)/4294967296>>>0}function ec(a){if(a<0){dc(0-a);var b=w(fc(C,cc));a=b.next().value;b=b.next().value;C=a>>>0;cc=b>>>0}else dc(a)}function hc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Jb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+ic(c)+ic(a));return c}
function ic(a){a=String(a);return"0000000".slice(a.length)+a}function jc(){var a=C,b=cc;b&2147483648?Jb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=w(fc(a,b)),a=b.next().value,b=b.next().value,a="-"+hc(a,b)):a=hc(a,b);return a}function fc(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var kc=typeof BigInt==="function"?BigInt.asIntN:void 0,lc=Number.isSafeInteger,mc=Number.isFinite,nc=Math.trunc;function oc(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function pc(a){return a.displayName||a.name||"unknown type name"}function qc(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+Aa(a)+": "+a);return a}var rc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;
function sc(a){switch(typeof a){case "bigint":return!0;case "number":return mc(a);case "string":return rc.test(a);default:return!1}}function tc(a){return a==null?a:mc(a)?a|0:void 0}function uc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return mc(a)?a|0:void 0}function vc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}
function wc(a){a.indexOf(".");if(vc(a))return a;if(a.length<16)ec(Number(a));else if(Jb())a=BigInt(a),C=Number(a&BigInt(4294967295))>>>0,cc=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");cc=C=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),cc*=1E6,C=C*1E6+d,C>=4294967296&&(cc+=Math.trunc(C/4294967296),cc>>>=0,C>>>=0);b&&(b=w(fc(C,cc)),a=b.next().value,b=b.next().value,C=a,cc=b)}return jc()}
function xc(a){sc(a);a=nc(a);if(!lc(a)){ec(a);var b=C,c=cc;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:hc(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function yc(a){sc(a);a=nc(a);if(lc(a))a=String(a);else{var b=String(a);vc(b)?a=b:(ec(a),a=jc())}return a}function zc(a){return a==null||typeof a==="string"?a:void 0}
function Ac(a,b,c,d){if(a!=null&&a[Bb]===Kb)return a;if(!Array.isArray(a))return c?d&2?b[wb]||(b[wb]=Bc(b)):new b:void 0;c=a[B]|0;d=c|d&32|d&2;d!==c&&Gb(a,d);return new b(a)}function Bc(a){a=new a;Ib(a.D);return a};function Cc(a){return a};function Dc(){}function Ec(a,b){for(var c in a)!isNaN(c)&&b(a,+c,a[c])}function Fc(a){var b=new Dc;Ec(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b}function Gc(a,b){b<100||rb(zb,1)};function Hc(a,b,c,d){var e=d!==void 0;d=!!d;var f=Ka(yb),g;!e&&sb&&f&&(g=a[f])&&Ec(g,Gc);f=[];var h=a.length;g=4294967295;var l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var t=h&&a[h-1];t!=null&&typeof t==="object"&&t.constructor===Object?(h--,g=h):t=void 0;if(m&&!(b&128)&&!e){l=!0;var x;g=((x=Ic)!=null?x:Cc)(g-n,n,a,t,void 0)+n}}b=void 0;for(x=0;x<h;x++){var v=a[x];if(v!=null&&(v=c(v,d))!=null)if(m&&x>=g){var N=x-n,Q=void 0;((Q=b)!=null?Q:b={})[N]=v}else f[x]=v}if(t)for(var R in t)h=t[R],h!=
null&&(h=c(h,d))!=null&&(x=+R,v=void 0,m&&!Number.isNaN(x)&&(v=x+n)<g?f[v]=h:(x=void 0,((x=b)!=null?x:b={})[R]=h));b&&(l?f.push(b):f[g]=b);e&&Ka(yb)&&(a=(c=Ka(yb))?a[c]:void 0)&&a instanceof Dc&&(f[yb]=Fc(a));return f}
function Jc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return bc(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[B]|0;return a.length===0&&b&1?void 0:Hc(a,b,Jc)}if(a!=null&&a[Bb]===Kb)return Kc(a);if(a instanceof lb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(ib){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):
b);b=btoa(c)}else{c===void 0&&(c=0);if(!gb){gb={};d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");e=["+/=","+/","-_=","-_.","-_"];for(var f=0;f<5;f++){var g=d.concat(e[f].split(""));fb[f]=g;for(var h=0;h<g.length;h++){var l=g[h];gb[l]===void 0&&(gb[l]=h)}}}c=fb[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=g=0;g<b.length-2;g+=3){var m=b[g],n=b[g+1];l=b[g+2];h=c[m>>2];m=c[(m&3)<<4|n>>4];n=c[(n&15)<<2|l>>6];l=c[l&63];d[f++]=""+h+m+n+l}h=0;l=e;switch(b.length-g){case 2:h=
b[g+1],l=c[(h&15)<<2]||e;case 1:b=b[g],d[f]=""+c[b>>2]+c[(b&3)<<4|h>>4]+l+e}b=d.join("")}a=a.g=b}return a}return}return a}var Lc=kb?structuredClone:function(a){return Hc(a,0,Jc)},Ic;function Kc(a){a=a.D;return Hc(a,a[B]|0,Jc)};function D(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[B]|0;if(Pa&&1&e)throw Error("rfarr");2048&e&&!(2&e)&&Mc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||Gb(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var l in h)f=
+l,f<g&&(c[f+b]=h[l],delete h[l]);e=e&-8380417|(g&1023)<<13;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("spvt");e=e&-8380417|(l&1023)<<13}}}e|=64;d===0&&(e|=2048);Gb(a,e);return a}function Mc(){if(Pa)throw Error("carr");rb(Ab,5)};function Nc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[B]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Oc(a,c,!1,b&&!(c&16)):(Hb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Bb]===Kb)return b=a.D,c=b[B]|0,Lb(a,c)?a:Pc(a,b,c)?Qc(a,b):Oc(b,c);if(a instanceof lb)return a}function Qc(a,b,c){a=new a.constructor(b);c&&(a.g=Mb);a.j=Mb;return a}function Oc(a,b,c,d){d!=null||(d=!!(34&b));a=Hc(a,b,Nc,d);d=32;c&&(d|=2);b=b&8380609|d;Gb(a,b);return a}
function Rc(a){var b=a.D,c=b[B]|0;return Lb(a,c)?Pc(a,b,c)?Qc(a,b,!0):new a.constructor(Oc(b,c,!1)):a}function Sc(a){if(a.g!==Mb)return!1;var b=a.D;b=Oc(b,b[B]|0);Hb(b,2048);a.D=b;a.g=void 0;a.j=void 0;return!0}function Tc(a,b){b===void 0&&(b=a[B]|0);b&32&&!(b&4096)&&Gb(a,b|4096)}function Pc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(Gb(b,c|2),a.g=Mb,!0):!1};var Uc=Ub(0),Vc={};function E(a,b,c,d,e){Object.isExtensible(a);b=Xc(a.D,b,c,e);if(b!==null||d&&a.j!==Mb)return b}function Xc(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}
function Yc(a,b,c){if(!Sc(a)&&Lb(a,a.D[B]|0))throw Error();var d=a.D;Zc(d,d[B]|0,b,c);return a}function Zc(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[B]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}
function $c(a,b,c,d,e,f,g,h){var l=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?ad(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==l&&Gb(a,b),Object.freeze(a)):(f===2&&ad(b)&&(a=Array.prototype.slice.call(a),l=0,b=bd(b,d),d=Zc(c,d,e,a)),ad(b)||(h||(b|=16),b!==l&&Gb(a,b)));2&b||!(4096&b||16&b)||Tc(c,d);return a}function cd(a,b){a=Xc(a,b);return Array.isArray(a)?a:Eb}function dd(a,b){2&b&&(a|=2);return a|1}function ad(a){return!!(2&a)&&!!(4&a)||!!(256&a)}
function ed(a){return a==null?a:typeof a==="string"?a?new lb(a,jb):mb():a.constructor===lb?a:hb&&a!=null&&a instanceof Uint8Array?a.length?new lb(new Uint8Array(a),jb):mb():void 0}function fd(a,b,c){return gd(a,b)===c?c:-1}
function gd(a,b){a=a.D;if(sb){var c;var d=(c=a[xb])!=null?c:a[xb]=new Map}else xb in a?d=a[xb]:(c=new Map,Object.defineProperty(a,xb,{value:c}),d=c);c=d;d=void 0;var e=c.get(b);if(e==null){for(var f=e=0;f<b.length;f++){var g=b[f];Xc(a,g)!=null&&(e!==0&&(d=Zc(a,d,e)),e=g)}c.set(b,e)}return e}function hd(a,b,c,d){var e=!1;d=Xc(a,d,void 0,function(f){var g=Ac(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!Lb(d)&&Tc(a,b),d}
function id(a,b,c){a=a.D;return hd(a,a[B]|0,b,c)||b[wb]||(b[wb]=Bc(b))}function F(a,b,c){var d=a.D,e=d[B]|0;b=hd(d,e,b,c);if(b==null)return b;e=d[B]|0;if(!Lb(a,e)){var f=Rc(b);f!==b&&(Sc(a)&&(d=a.D,e=d[B]|0),b=f,e=Zc(d,e,c,b),Tc(d,e))}return b}
function jd(a,b,c){var d=void 0===Nb?2:4,e=a.D,f=e;e=e[B]|0;var g=Lb(a,e),h=g?1:d;d=h===3;var l=!g;(h===2||l)&&Sc(a)&&(f=a.D,e=f[B]|0);a=cd(f,c);var m=a===Eb?7:a[B]|0,n=dd(m,e);if(g=!(4&n)){var t=a,x=e,v=!!(2&n);v&&(x|=2);for(var N=!v,Q=!0,R=0,Wa=0;R<t.length;R++){var Xa=Ac(t[R],b,!1,x);if(Xa instanceof b){if(!v){var ub=Lb(Xa);N&&(N=!ub);Q&&(Q=ub)}t[Wa++]=Xa}}Wa<R&&(t.length=Wa);n|=4;n=Q?n&-4097:n|4096;n=N?n|8:n&-9}n!==m&&(Gb(a,n),2&n&&Object.freeze(a));if(l&&!(8&n||!a.length&&(h===1||(h!==4?0:2&
n||!(16&n)&&32&e)))){ad(n)&&(a=Array.prototype.slice.call(a),n=bd(n,e),e=Zc(f,e,c,a));b=a;l=n;for(m=0;m<b.length;m++)t=b[m],n=Rc(t),t!==n&&(b[m]=n);l|=8;n=l=b.length?l|4096:l&-4097;Gb(a,n)}return a=$c(a,n,f,e,c,h,g,d)}function kd(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+pc(b)+" but got "+(d&&pc(d.constructor)));}else d=void 0;Yc(a,c,d);d&&!Lb(d)&&Tc(a.D);return a}function bd(a,b){return a=(2&b?a|2:a&-3)&-273}
function ld(a,b){var c=c===void 0?!1:c;a=E(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c}function md(a,b,c){c=c===void 0?0:c;var d;return(d=uc(E(a,b)))!=null?d:c}function nd(a,b){var c=c===void 0?Uc:c;a=E(a,b);b=typeof a;a!=null&&(b==="bigint"?a=Ub(kc(64,a)):sc(a)?b==="string"?(b=nc(Number(a)),lc(b)?a=Ub(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Jb()?Ub(kc(64,BigInt(a))):Ub(wc(a)))):a=lc(a)?Ub(xc(a)):Ub(yc(a)):a=void 0);return a!=null?a:c}
function od(a,b){var c=c===void 0?"":c;var d;return(d=zc(E(a,b)))!=null?d:c}function pd(a,b){var c=c===void 0?0:c;var d;return(d=tc(E(a,b)))!=null?d:c}function qd(a,b){return zc(E(a,b,void 0,Vc))}function rd(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Yc(a,b,c)}function sd(a,b){if(b!=null){if(!mc(b))throw a=Error("enum"),ob(a,"severity","warning"),a;b|=0}return Yc(a,1,b)};function G(a,b,c){this.D=D(a,b,c)}G.prototype.toJSON=function(){return Kc(this)};function td(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Hb(b,32);return new a(b)}G.prototype.clone=function(){var a=this.D,b=a[B]|0;return Pc(this,a,b)?Qc(this,a,!0):new this.constructor(Oc(a,b,!1))};G.prototype[Bb]=Kb;G.prototype.toString=function(){return this.D.toString()};
function ud(a,b){if(b==null)b=a.constructor,b=b[wb]||(b[wb]=Bc(b));else{a=a.constructor;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();b=new a(Ib(b))}return b};function vd(a){return function(b){return td(a,b)}};function wd(a){this.D=D(a)}u(wd,G);wd.prototype.getTypeName=function(){return od(this,1).split("/").pop()};var xd=function(a){return Ob(function(b){return b instanceof a&&!Lb(b)})}(wd);function yd(){var a=zd("[]"),b=Ad;this.key="45696263";this.defaultValue=a;this.g=b;this.flagNameForDebugging=void 0}
yd.prototype.ctor=function(a){if(typeof a==="string"&&a)return td(this.g,a);if(!xd(a))return this.defaultValue.clone();var b;try{var c,d=this.g,e=(c=a.getTypeName())!=null?c:"";if(od(a,1).split("/").pop()!=e)var f=null;else{var g=typeof d==="function"?d:d.constructor,h=a.D,l=h[B]|0,m=Xc(h,2);Sc(a)&&(h=a.D,l=h[B]|0);a=h;if(m!=null&&!(Array.isArray(m)||m!=null&&m[Bb]===Kb))throw Error("saw an invalid value of type '"+Aa(m)+"' in the Any.value field");var n=Ac(m,g,!0,l);if(!(n instanceof g))throw Error("incorrect type in any value: got "+
n.constructor.displayName+", expected "+g.displayName);(g=!!(2&l))||(n=Rc(n));m!==n&&(Zc(a,l,2,n),g||Tc(a));f=n}}catch(t){f=null}return(b=f)!=null?b:this.defaultValue.clone()};function Bd(a){this.D=D(a)}u(Bd,G);var Cd=[1,2];function Dd(a){this.D=D(a)}u(Dd,G);var Ed=[2,3,4,5,6,8];function Fd(a){this.D=D(a)}u(Fd,G);Fd.prototype.Sa=function(){var a=E(this,3,void 0,void 0,ed);return a==null?mb():a};function Gd(a){this.D=D(a)}u(Gd,G);var Hd=vd(Gd);function Ad(a){this.D=D(a)}u(Ad,G);var zd=vd(Ad);function Id(a,b){this.K=a|0;this.I=b|0}function Jd(a){return a.I*4294967296+(a.K>>>0)}q=Id.prototype;q.isSafeInteger=function(){var a=this.I>>21;return a==0||a==-1&&!(this.K==0&&this.I==-2097152)};
q.toString=function(a){a=a||10;if(a<2||36<a)throw Error("radix out of range: "+a);if(this.isSafeInteger()){var b=Jd(this);return a==10?""+b:b.toString(a)}b=14-(a>>2);var c=Math.pow(a,b),d=H(c,c/4294967296);c=this.div(d);var e=Math,f=e.abs;d=c.multiply(d);d=this.add(Kd(d));e=f.call(e,Jd(d));f=a==10?""+e:e.toString(a);f.length<b&&(f="0000000000000".slice(f.length-b)+f);e=Jd(c);return(a==10?e:e.toString(a))+f};function Ld(a){return a.K==0&&a.I==0}q.S=function(){return this.K^this.I};
q.equals=function(a){return a==null?!1:this.K==a.K&&this.I==a.I};q.compare=function(a){return this.I==a.I?this.K==a.K?0:this.K>>>0>a.K>>>0?1:-1:this.I>a.I?1:-1};function Kd(a){var b=~a.K+1|0;return H(b,~a.I+!b|0)}q.add=function(a){var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=a.I>>>16,f=a.I&65535,g=a.K>>>16;a=(this.K&65535)+(a.K&65535);g=(a>>>16)+(d+g);d=g>>>16;d+=c+f;return H((g&65535)<<16|a&65535,((d>>>16)+(b+e)&65535)<<16|d&65535)};
q.multiply=function(a){if(Ld(this))return this;if(Ld(a))return a;var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=this.K&65535,f=a.I>>>16,g=a.I&65535,h=a.K>>>16;a=a.K&65535;var l=e*a;var m=(l>>>16)+d*a;var n=m>>>16;m=(m&65535)+e*h;n+=m>>>16;n+=c*a;var t=n>>>16;n=(n&65535)+d*h;t+=n>>>16;n=(n&65535)+e*g;t=t+(n>>>16)+(b*a+c*h+d*g+e*f)&65535;return H((m&65535)<<16|l&65535,t<<16|n&65535)};
q.div=function(a){if(Ld(a))throw Error("division by zero");if(this.I<0){if(this.equals(Md)){if(a.equals(Nd)||a.equals(Od))return Md;if(a.equals(Md))return Nd;var b=this.I;b=H(this.K>>>1|b<<31,b>>1);b=b.div(a).shiftLeft(1);if(b.equals(Pd))return a.I<0?Nd:Od;var c=a.multiply(b);c=this.add(Kd(c));return b.add(c.div(a))}return a.I<0?Kd(this).div(Kd(a)):Kd(Kd(this).div(a))}if(Ld(this))return Pd;if(a.I<0)return a.equals(Md)?Pd:Kd(this.div(Kd(a)));b=Pd;for(c=this;c.compare(a)>=0;){var d=Math.max(1,Math.floor(Jd(c)/
Jd(a))),e=Math.ceil(Math.log(d)/Math.LN2);e=e<=48?1:Math.pow(2,e-48);for(var f=Qd(d),g=f.multiply(a);g.I<0||g.compare(c)>0;)d-=e,f=Qd(d),g=f.multiply(a);Ld(f)&&(f=Nd);b=b.add(f);c=c.add(Kd(g))}return b};q.and=function(a){return H(this.K&a.K,this.I&a.I)};q.or=function(a){return H(this.K|a.K,this.I|a.I)};q.xor=function(a){return H(this.K^a.K,this.I^a.I)};q.shiftLeft=function(a){a&=63;if(a==0)return this;var b=this.K;return a<32?H(b<<a,this.I<<a|b>>>32-a):H(0,b<<a-32)};
function Qd(a){return a>0?a>=0x7fffffffffffffff?Rd:new Id(a,a/4294967296):a<0?a<=-0x7fffffffffffffff?Md:Kd(new Id(-a,-a/4294967296)):Pd}function H(a,b){return new Id(a,b)}var Pd=H(0,0),Nd=H(1,0),Od=H(-1,-1),Rd=H(4294967295,2147483647),Md=H(0,2147483648);function Sd(a,b){b=b===void 0?window:b;b=b===void 0?window:b;return(b=b.WIZ_global_data)&&a in b?b[a]:null};var Td;
function Ud(){var a=Hd("["+Sd("TSDtV",window).substring(4));if(a=jd(a,Fd,1)[0])for(var b=w(jd(a,Dd,2)),c=b.next();!c.done;c=b.next()){c=c.value;var d=c.D;if(hd(d,d[B]|0,wd,fd(c,Ed,6))!==void 0)throw Error();}if(a)for(b={},c=w(jd(a,Dd,2)),d=c.next();!d.done;d=c.next()){var e=d.value;d=nd(e,1).toString();switch(gd(e,Ed)){case 3:b[d]=ld(e,fd(e,Ed,3));break;case 2:var f=nd(e,fd(e,Ed,2));Vb(f);bc(f);f=bc(f)?Number(f):String(f);b[d]=f;break;case 4:f=void 0;var g=e;var h=fd(e,Ed,4);e=void 0;e=e===void 0?
0:e;g=(f=E(g,h,void 0,void 0,oc))!=null?f:e;b[d]=g;break;case 5:b[d]=od(e,fd(e,Ed,5));break;case 6:b[d]=F(e,wd,fd(e,Ed,6));break;case 8:f=id(e,Bd,fd(e,Ed,8));switch(gd(f,Cd)){case 1:b[d]=od(f,fd(f,Cd,1));break;default:throw Error("case "+gd(f,Cd));}break;default:throw Error("case "+gd(e,Ed));}}else b={};this.g=b;this.j=a?a.Sa():null}Ud.prototype.Sa=p("j");function Vd(a){this.D=D(a)}u(Vd,G);var Wd=new yd;function Xd(a){this.D=D(a)}u(Xd,G);var Yd=function(a){return function(){return a[wb]||(a[wb]=Bc(a))}}(Xd);Object.create(null);function I(){}I.prototype.equals=function(a){return J(this,a)};I.prototype.S=function(){return Zd(this)};I.prototype.toString=function(){return K($d(L(ae(this))))+"@"+K((this.S()>>>0).toString(16))};function be(a){return a!=null}I.prototype.v=["java.lang.Object",0];function ce(){}u(ce,I);function de(a){ee(a);fe(a)}function ge(a,b){ee(a);a.j=b;fe(a)}function M(a,b){a.g=b;he(b,a)}function fe(a){ie(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(O(a.g,ie,je)):O(a.g,ie,je).stack=Error().stack);a.l=null}ce.prototype.toString=function(){var a=$d(L(ae(this))),b=this.j;return b==null?a:K(a)+": "+K(b)};
function ke(a){if(a!=null){var b=a.hb;if(b!=null)return b}a instanceof TypeError?b=le():(b=new me,de(b),M(b,Error(b)));b.j=a==null?"null":a.toString();M(b,a);return b}function ee(a){a.l=ne([0],oe,pe)}function qe(a){return a instanceof ce}ce.prototype.v=["java.lang.Throwable",0];function re(){}u(re,ce);re.prototype.v=["java.lang.Exception",0];function P(){}u(P,re);P.prototype.v=["java.lang.RuntimeException",0];function se(){}u(se,P);function te(){var a=new se;de(a);M(a,Error(a));return a}function ue(a){var b=new se;ge(b,a);M(b,Error(b));return b}se.prototype.v=["java.lang.IndexOutOfBoundsException",0];var ve;function we(){we=k();for(var a=ne([256],xe,ye),b=0;b<256;b=b+1|0)ze(a,b,Ae(b-128|0));ve=a};function Be(){}u(Be,P);Be.prototype.v=["java.lang.ArithmeticException",0];function Ce(){}u(Ce,P);Ce.prototype.v=["java.lang.ArrayStoreException",0];function De(){}u(De,P);De.prototype.v=["java.lang.ClassCastException",0];function Ee(){}u(Ee,P);function Fe(a){var b=new Ee;ge(b,a);M(b,Error(b));return b}Ee.prototype.v=["java.lang.IllegalArgumentException",0];function Ge(){}u(Ge,P);function He(){var a=new Ge;de(a);M(a,Error(a));return a}Ge.prototype.v=["java.lang.IllegalStateException",0];function me(){}u(me,P);me.prototype.v=["java.lang.JsException",0];function Ie(){}u(Ie,me);function le(){var a=new Ie;de(a);M(a,new TypeError(a));return a}Ie.prototype.v=["java.lang.NullPointerException",0];function Je(){}u(Je,se);function Ke(a){var b=new Je;ge(b,a);M(b,Error(b));return b}Je.prototype.v=["java.lang.StringIndexOutOfBoundsException",0];function Le(){}u(Le,P);function Me(){var a=new Le;de(a);M(a,Error(a));return a}Le.prototype.v=["java.util.ConcurrentModificationException",0];function Ne(){}u(Ne,P);function Oe(){var a=new Ne;de(a);M(a,Error(a));return a}Ne.prototype.v=["java.util.NoSuchElementException",0];function Pe(){}var Qe;u(Pe,I);Pe.prototype.v=["java.lang.Number",0];function Re(){}u(Re,Pe);Re.prototype.v=["java.lang.Double",0];function Se(a){return Qd(a)}function Te(a){if(!isFinite(a))throw a=new Be,de(a),M(a,Error(a)),a.g;return a|0}function Ue(a){return Math.max(Math.min(a,2147483647),-2147483648)|0};function Ve(){}u(Ve,I);Ve.prototype.v=["java.lang.Boolean",0];function O(a,b,c){a==null||b(a)||(b=K($d(We(a)))+" cannot be cast to "+K($d(L(c))),Xe(b));return a};function ae(a){return a.constructor}function Ye(a,b,c){if(Object.prototype.hasOwnProperty.call(a.prototype,b))return a.prototype[b];c=c();return a.prototype[b]=c};function J(a,b){return Object.is(a,b)||a==null&&b==null};function Ze(a){switch(S(typeof a)){case "string":for(var b=0,c=0;c<a.length;c=c+1|0){b=(b<<5)-b;var d=a,e=c;$e(e,d.length);b=b+d.charCodeAt(e)|0}return b;case "number":return a=S(a),Ue(a);case "boolean":return S(a)?1231:1237;default:return a==null?0:Zd(a)}}var af=0;function Zd(a){return a.La||(Object.defineProperties(a,{La:{value:af=af+1|0,enumerable:!1}}),a.La)};function bf(a,b){return a.equals?a.equals(b):Object.is(a,b)}function cf(a){return a.S?a.S():Ze(a)}function We(a){switch(S(typeof a)){case "number":return L(Re);case "boolean":return L(Ve);case "string":return L(df);case "function":return L(ef)}if(a instanceof Id)a=L(ff);else if(a instanceof I)a=L(ae(a));else if(Array.isArray(a))a=(a=a.V)?L(a.ia,a.ha):L(I,1);else if(a!=null)a=L(gf);else throw new TypeError("null.getClass()");return a};function ef(){}ef.prototype.v=["<native function>",1];function gf(){}u(gf,I);gf.prototype.v=["<native object>",0];function hf(){}u(hf,P);function jf(){var a=new hf;de(a);M(a,Error(a));return a}hf.prototype.v=["java.lang.UnsupportedOperationException",0];function T(a,b){return J(a,b)||a!=null&&bf(a,b)}function kf(a){return a!=null?cf(a):0}function lf(a){if(a==null)throw le().g;};function xe(){this.ca=0}u(xe,Pe);function mf(a){a>-129&&a<128?(we(),a=ve[a+128|0]):a=Ae(a);return a}function Ae(a){var b=new xe;b.ca=a;return b}xe.prototype.equals=function(a){return ye(a)&&O(a,ye,xe).ca==this.ca};xe.prototype.S=p("ca");xe.prototype.toString=function(){return""+this.ca};function ye(a){return a instanceof xe}xe.prototype.v=["java.lang.Integer",0];function ff(){}u(ff,Pe);ff.prototype.v=["java.lang.Long",0];function nf(){}u(nf,I);q=nf.prototype;q.add=function(){throw jf().g;};q.za=function(a){S(a);var b=!1;for(a=a.F();a.g();){var c=a.j();b=!!(+b|+this.add(c))}};q.clear=function(){for(var a=this.F();a.g();)a.j(),a.l()};q.contains=function(a){return of(this,a,!1)};q.Ba=function(a){S(a);for(a=a.F();a.g();){var b=a.j();if(!this.contains(b))return!1}return!0};q.remove=function(a){return of(this,a,!0)};q.removeAll=function(a){S(a);for(var b=!1,c=this.F();c.g();){var d=c.j();a.contains(d)&&(c.l(),b=!0)}return b};
q.fa=function(){return pf(this,Array(this.size()))};q.ma=function(a){return pf(this,a)};q.toString=function(){for(var a=qf("[","]"),b=this.F();b.g();){var c=b.j();rf(a,J(c,this)?"(this Collection)":K(c))}return a.toString()};function of(a,b,c){for(a=a.F();a.g();){var d=a.j();if(T(b,d))return c&&a.l(),!0}return!1}q.Xa=function(){return this.fa()};q.v=["java.util.AbstractCollection",0];function sf(){}function tf(){var a=new uf;a.j=1;a.g=1;return vf(a,wf,xf)}function yf(a){return zf(a.slice(0,a.length))}function vf(){return zf(ra.apply(0,arguments))}function Af(a){return a!=null&&!!a.oa}sf.prototype.oa=!0;sf.prototype.v=["java.util.List",1];function Bf(){}u(Bf,nf);q=Bf.prototype;q.add=function(a){this.ra(this.size(),a);return!0};q.ra=function(){throw jf().g;};q.Aa=function(a,b){S(b);for(b=b.F();b.g();){var c=b.j(),d=void 0;this.ra((d=a,a=a+1|0,d),c)}};q.clear=function(){this.Va(0,this.size())};q.equals=function(a){if(J(a,this))return!0;if(!Af(a))return!1;a=O(a,Af,sf);if(this.size()!=a.size())return!1;a=a.F();for(var b=this.F();b.g();){var c=b.j(),d=a.j();if(!T(c,d))return!1}return!0};
q.S=function(){Cf();for(var a=1,b=this.F();b.g();){var c=b.j();a=Math.imul(31,a)+kf(c)|0}return a};q.indexOf=function(a){for(var b=0,c=this.size();b<c;b=b+1|0)if(T(a,this.Z(b)))return b;return-1};q.F=function(){var a=new Df;a.A=this;a.o=0;a.B=-1;return a};q.lastIndexOf=function(a){for(var b=this.size()-1|0;b>-1;b=b-1|0)if(T(a,this.Z(b)))return b;return-1};q.Fa=function(a){var b=new Ef;b.A=this;b.o=0;b.B=-1;Ff(a,this.size());b.o=a;return b};q.Ia=function(){throw jf().g;};
q.Va=function(a,b){for(var c=this.Fa(a);a<b;a=a+1|0)c.j(),c.l()};q.oa=!0;q.v=["java.util.AbstractList",0];function Gf(){}u(Gf,Bf);q=Gf.prototype;q.za=function(a){this.Aa(this.g.length,a)};q.contains=function(a){return this.indexOf(a)!=-1};q.Z=function(a){Hf(a,this.g.length);return this.g[a]};q.indexOf=function(a){a:{for(var b=0,c=this.g.length;b<c;b=b+1|0)if(T(a,this.g[b])){a=b;break a}a=-1}return a};q.F=function(){var a=new If;a.A=this;a.o=0;a.B=-1;return a};q.lastIndexOf=function(a){a:{for(var b=this.g.length-1|0;b>=0;b=b-1|0)if(T(a,this.g[b])){a=b;break a}a=-1}return a};
q.Ia=function(a){this.Z(a);this.g.splice(a,1)};q.remove=function(a){a=this.indexOf(a);if(a==-1)return!1;this.g.splice(a,1);return!0};q.size=function(){return this.g.length};q.ma=function(a){var b=this.g.length;a.length<b&&(a=Jf(Array(b),a));for(var c=0;c<b;c=c+1|0)ze(a,c,this.g[c]);a.length>b&&ze(a,b,null);return a};q.oa=!0;q.v=["java.util.ArrayListBase",0];function Kf(){}u(Kf,Gf);function Lf(){var a=new Kf;a.g=[];return a}q=Kf.prototype;q.add=function(a){this.g.push(a);return!0};q.ra=function(a,b){Ff(a,this.g.length);this.g.splice(a,0,b)};q.Aa=function(a,b){Ff(a,this.g.length);b=b.fa();var c=b.length;if(c!=0){var d=this.g.length+c|0;this.g.length=d;var e=a+c|0;Mf(this.g,a,this.g,e,d-e|0);Mf(b,0,this.g,a,c)}};q.fa=function(){var a=this.g,b=a.slice();b.V=a.V;b==null||Nf(b,I,be,1)||(a=L(I,1),a=$d(We(b))+" cannot be cast to "+$d(a),Xe(a));return b};
q.Va=function(a,b){var c=this.g.length;if(a<0||b>c)throw ue("fromIndex: "+a+", toIndex: "+b+", size: "+c).g;if(a>b)throw Fe("fromIndex: "+a+" > toIndex: "+b).g;this.g.splice(a,b-a|0)};q.v=["java.util.ArrayList",0];function If(){this.B=this.o=0}u(If,I);If.prototype.g=function(){return this.o<this.A.g.length};If.prototype.j=function(){Of(this.g());var a;this.B=(a=this.o,this.o=this.o+1|0,a);return this.A.g[this.B]};If.prototype.l=function(){Pf(this.B!=-1);var a=this.A,b=this.o=this.B;a.g.splice(b,1);this.B=-1};If.prototype.v=["java.util.ArrayListBase$1",0];function Qf(){}u(Qf,Bf);q=Qf.prototype;q.contains=ba(!1);q.Z=function(a){Hf(a,0);return null};q.F=function(){return Rf()};q.size=ba(0);q.v=["java.util.Collections$EmptyList",0];function Sf(){}var Tf;u(Sf,I);Sf.prototype.g=ba(!1);Sf.prototype.j=function(){throw Oe().g;};Sf.prototype.l=function(){throw He().g;};function Uf(){Uf=k();Tf=new Sf}Sf.prototype.v=["java.util.Collections$EmptyListIterator",0];function Vf(){}u(Vf,I);Vf.prototype.g=function(){return this.o.g()};Vf.prototype.j=function(){return O(this.o.j(),U,V).O()};Vf.prototype.l=function(){this.o.l()};Vf.prototype.v=["java.util.AbstractMap$1$1",0];function V(){}function U(a){return a!=null&&!!a.xa}V.prototype.xa=!0;V.prototype.v=["java.util.Map$Entry",1];function Wf(){}function Xf(){var a=ra.apply(0,arguments);Cf();if(a.length==0)a=Yf(Zf);else{var b=new $f;b.g=ag();for(var c=0;c<a.length;c=c+1|0)if(!b.add(S(a[c])))throw Fe("Duplicate element").g;a=Yf(b)}return a}function bg(a){return a!=null&&!!a.pa}Wf.prototype.pa=!0;Wf.prototype.v=["java.util.Set",1];function cg(){}u(cg,nf);q=cg.prototype;q.equals=function(a){if(J(a,this))return!0;if(!bg(a))return!1;a=O(a,bg,Wf);return a.size()!=this.size()?!1:this.Ba(a)};q.S=function(){return dg(this)};q.removeAll=function(a){S(a);var b=this.size();if(b<a.size())for(var c=this.F();c.g();){var d=c.j();a.contains(d)&&c.l()}else for(a=a.F();a.g();)c=a.j(),this.remove(c);return b!=this.size()};q.pa=!0;q.v=["java.util.AbstractSet",0];function eg(){}u(eg,cg);q=eg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ja(a)};q.F=function(){var a=this.g.aa().F(),b=new Vf;b.o=a;return b};q.remove=function(a){return this.g.ja(a)?(this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.v=["java.util.AbstractMap$1",0];function fg(){}u(fg,I);fg.prototype.g=function(){return this.o.g()};fg.prototype.j=function(){return O(this.o.j(),U,V).R()};fg.prototype.l=function(){this.o.l()};fg.prototype.v=["java.util.AbstractMap$2$1",0];function gg(){}u(gg,nf);q=gg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.Na(a)};q.F=function(){var a=this.g.aa().F(),b=new fg;b.o=a;return b};q.size=function(){return this.g.size()};q.v=["java.util.AbstractMap$2",0];function hg(){}u(hg,I);q=hg.prototype;q.O=p("j");q.R=p("g");q.Ma=function(a){var b=this.g;this.g=a;return b};q.equals=function(a){if(!U(a))return!1;a=O(a,U,V);return T(this.j,a.O())&&T(this.g,a.R())};q.S=function(){return kf(this.j)^kf(this.g)};q.toString=function(){return K(this.j)+"="+K(this.g)};q.xa=!0;q.v=["java.util.AbstractMap$AbstractEntry",0];function ig(){}u(ig,hg);function jg(a,b){var c=new ig;c.j=a;c.g=b;return c}ig.prototype.v=["java.util.AbstractMap$SimpleEntry",0];function kg(){}function lg(a){return a!=null&&!!a.Ka}kg.prototype.Ka=!0;kg.prototype.v=["java.util.Map",1];function mg(){}u(mg,I);q=mg.prototype;q.clear=function(){this.aa().clear()};q.ja=function(a){return ng(this,a,!1)!=null};q.Na=function(a){for(var b=this.aa().F();b.g();){var c=O(b.j(),U,V).R();if(T(a,c))return!0}return!1};function og(a,b){var c=b.O();b=b.R();var d=a.get(c);return!T(b,d)||d==null&&!a.ja(c)?!1:!0}q.equals=function(a){if(J(a,this))return!0;if(!lg(a))return!1;a=O(a,lg,kg);if(this.size()!=a.size())return!1;for(a=a.aa().F();a.g();){var b=O(a.j(),U,V);if(!og(this,b))return!1}return!0};
q.get=function(a){return pg(ng(this,a,!1))};q.S=function(){return dg(this.aa())};q.Ua=function(){var a=new eg;a.g=this;return a};q.W=function(){throw jf().g;};q.remove=function(a){return pg(ng(this,a,!0))};q.size=function(){return this.aa().size()};q.toString=function(){for(var a=qf("{","}"),b=this.aa().F();b.g();){var c=O(b.j(),U,V);c=K(qg(this,c.O()))+"="+K(qg(this,c.R()));rf(a,c)}return a.toString()};function qg(a,b){return J(b,a)?"(this Map)":K(b)}q.values=function(){var a=new gg;a.g=this;return a};
function pg(a){return a==null?null:a.R()}function ng(a,b,c){for(a=a.aa().F();a.g();){var d=O(a.j(),U,V);if(T(b,d.O()))return c&&(d=jg(d.O(),d.R()),a.l()),d}return null}q.Ka=!0;q.v=["java.util.AbstractMap",0];function rg(){}u(rg,I);rg.prototype.toString=p("g");rg.prototype.v=["java.lang.AbstractStringBuilder",0];function sg(){}u(sg,rg);sg.prototype.v=["java.lang.StringBuilder",0];function tg(){}u(tg,I);function qf(a,b){var c=new tg;c.o=", ".toString();c.l=a.toString();c.j=b.toString();c.B=K(c.l)+K(c.j);return c}function rf(a,b){if(a.g==null){var c=new sg,d=O(S(a.l),ug,df);c.g=d;a.g=c}else c=a.g,c.g=K(c.g)+K(a.o);a=a.g;a.g=K(a.g)+K(b)}tg.prototype.toString=function(){return this.g==null?this.B:this.j.length==0?this.g.toString():K(this.g.toString())+K(this.j)};tg.prototype.v=["java.util.StringJoiner",0];function vg(){}u(vg,cg);vg.prototype.contains=ba(!1);vg.prototype.F=function(){return Rf()};vg.prototype.size=ba(0);vg.prototype.v=["java.util.Collections$EmptySet",0];function wg(){}u(wg,I);q=wg.prototype;q.add=function(){throw jf().g;};q.za=function(){throw jf().g;};q.clear=function(){throw jf().g;};q.contains=function(a){return this.g.contains(a)};q.Ba=function(a){return this.g.Ba(a)};q.F=function(){var a=this.g.F(),b=new xg;b.o=a;return b};q.remove=function(){throw jf().g;};q.removeAll=function(){throw jf().g;};q.size=function(){return this.g.size()};q.fa=function(){return this.g.fa()};q.ma=function(a){return this.g.ma(a)};q.toString=function(){return this.g.toString()};
q.Xa=function(){return this.fa()};q.v=["java.util.Collections$UnmodifiableCollection",0];function xg(){}u(xg,I);xg.prototype.g=function(){return this.o.g()};xg.prototype.j=function(){return this.o.j()};xg.prototype.l=function(){throw jf().g;};xg.prototype.v=["java.util.Collections$UnmodifiableCollectionIterator",0];function yg(){}u(yg,wg);q=yg.prototype;q.ra=function(){throw jf().g;};q.Aa=function(){throw jf().g;};q.equals=function(a){return bf(this.j,a)};q.Z=function(a){return this.j.Z(a)};q.S=function(){return cf(this.j)};q.indexOf=function(a){return this.j.indexOf(a)};q.lastIndexOf=function(a){return this.j.lastIndexOf(a)};q.Fa=function(a){a=this.j.Fa(a);var b=new zg;b.o=a;return b};q.Ia=function(){throw jf().g;};q.oa=!0;q.v=["java.util.Collections$UnmodifiableList",0];function zg(){}u(zg,xg);zg.prototype.v=["java.util.Collections$UnmodifiableListIterator",0];function Ag(){}u(Ag,wg);Ag.prototype.equals=function(a){return bf(this.g,a)};Ag.prototype.S=function(){return cf(this.g)};Ag.prototype.pa=!0;Ag.prototype.v=["java.util.Collections$UnmodifiableSet",0];function Bg(){}u(Bg,yg);Bg.prototype.v=["java.util.Collections$UnmodifiableRandomAccessList",0];var Cg,Zf;function Rf(){Cf();return Uf(),Tf}function zf(a){Cf();for(var b=0;b<a.length;b=b+1|0)S(a[b]);a.length==0?b=Cg:(b=new Dg,S(a),b.g=a);a=new Bg;a.g=b;a.j=b;return a}function Yf(a){Cf();var b=new Ag;b.g=a;return b}function dg(a){Cf();var b=0;for(a=a.F();a.g();){var c=a.j();b=b+kf(c)|0}return b}function Cf(){Cf=k();Cg=new Qf;Zf=new vg};function Eg(){}u(Eg,cg);q=Eg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return U(a)?og(this.g,O(a,U,V)):!1};q.F=function(){var a=new Fg;a.o=this.g;a.H=a.o.l.F();a.B=a.H;a.A=Gg(a);a.G=a.o.j;return a};q.remove=function(a){return this.contains(a)?(a=O(a,U,V).O(),this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.v=["java.util.AbstractHashMap$EntrySet",0];function Fg(){this.A=!1;this.G=0}u(Fg,I);Fg.prototype.g=p("A");function Gg(a){if(a.B.g())return!0;if(!J(a.B,a.H))return!1;a.B=a.o.g.F();return a.B.g()}Fg.prototype.l=function(){Pf(this.C!=null);if(this.o.j!=this.G)throw Me().g;this.C.l();this.C=null;this.A=Gg(this);this.G=this.o.j};Fg.prototype.j=function(){if(this.o.j!=this.G)throw Me().g;Of(this.g());this.C=this.B;var a=O(this.B.j(),U,V);this.A=Gg(this);return a};Fg.prototype.v=["java.util.AbstractHashMap$EntrySetIterator",0];function Hg(){this.j=0}u(Hg,mg);q=Hg.prototype;q.clear=function(){Ig(this)};function Ig(a){var b=new Jg;b.j=new Map;b.l=a;a.g=b;b=new Kg;b.g=new Map;b.o=a;a.l=b;Lg(a)}function Lg(a){a.j=a.j+1|0}q.ja=function(a){return ug(a)?this.l.g.has(a):Mg(a,Ng(this.g,a==null?0:cf(a)))!=null};q.Na=function(a){return Og(a,this.l)||Og(a,this.g)};function Og(a,b){for(b=b.F();b.g();){var c=O(b.j(),U,V),d=a;c=c.R();if(T(d,c))return!0}return!1}q.aa=function(){var a=new Eg;a.g=this;return a};
q.get=function(a){return ug(a)?this.l.g.get(a):pg(Mg(a,Ng(this.g,a==null?0:cf(a))))};q.W=function(a,b){if(ug(a))a=Pg(this.l,a,b);else a:{var c=this.g,d=a==null?0:cf(a),e=Ng(c,d);if(e.length==0)c.j.set(d,e);else if(d=Mg(a,e),d!=null){a=d.Ma(b);break a}ze(e,e.length,jg(a,b));c.g=c.g+1|0;Lg(c.l);a=null}return a};q.remove=function(a){return ug(a)?Qg(this.l,a):Rg(this.g,a)};q.size=function(){return this.g.g+this.l.l|0};q.v=["java.util.AbstractHashMap",0];function Sg(){this.o=0}u(Sg,I);Sg.prototype.g=function(){if(this.o<this.B.length)return!0;var a=this.C.next();return a.done?!1:(this.B=a.value[1],this.o=0,!0)};Sg.prototype.l=function(){Rg(this.G,this.A.O());this.o!=0&&(this.o=this.o-1|0)};Sg.prototype.j=function(){var a;return this.A=this.B[a=this.o,this.o=this.o+1|0,a]};Sg.prototype.v=["java.util.InternalHashCodeMap$1",0];function Jg(){this.g=0}u(Jg,I);function Rg(a,b){for(var c=b==null?0:cf(b),d=Ng(a,c),e=0;e<d.length;e=e+1|0){var f=d[e];if(T(b,f.O()))return d.length==1?(d.length=0,a.j.delete(c)):d.splice(e,1),a.g=a.g-1|0,Lg(a.l),f.R()}return null}function Mg(a,b){for(var c=0;c<b.length;c++){var d=b[c];if(T(a,d.O()))return d}return null}Jg.prototype.F=function(){var a=new Sg;a.G=this;a.C=a.G.j.entries();a.o=0;a.B=[];a.A=null;return a};function Ng(a,b){a=a.j.get(b);return a==null?[]:a}
Jg.prototype.v=["java.util.InternalHashCodeMap",0];function Tg(){}u(Tg,I);Tg.prototype.g=function(){return!this.B.done};Tg.prototype.l=function(){Qg(this.o,this.G.value[0])};Tg.prototype.j=function(){this.G=this.B;this.B=this.A.next();var a=new Ug,b=this.G,c=this.o.j;a.j=this.o;a.g=b;a.l=c;return a};Tg.prototype.v=["java.util.InternalStringMap$1",0];function Vg(){}u(Vg,I);q=Vg.prototype;q.equals=function(a){if(!U(a))return!1;a=O(a,U,V);return T(this.O(),a.O())&&T(this.R(),a.R())};q.S=function(){return kf(this.O())^kf(this.R())};q.toString=function(){return K(this.O())+"="+K(this.R())};q.xa=!0;q.v=["java.util.AbstractMapEntry",0];function Ug(){this.l=0}u(Ug,Vg);Ug.prototype.O=function(){return this.g.value[0]};Ug.prototype.R=function(){return this.j.j!=this.l?this.j.g.get(this.g.value[0]):this.g.value[1]};Ug.prototype.Ma=function(a){return Pg(this.j,this.g.value[0],a)};Ug.prototype.v=["java.util.InternalStringMap$2",0];function Kg(){this.j=this.l=0}u(Kg,I);function Pg(a,b,c){var d=a.g.get(b);a.g.set(b,c===void 0?null:c);d===void 0?(a.l=a.l+1|0,Lg(a.o)):a.j=a.j+1|0;return d}function Qg(a,b){var c=a.g.get(b);c===void 0?a.j=a.j+1|0:(a.g.delete(b),a.l=a.l-1|0,Lg(a.o));return c}Kg.prototype.F=function(){var a=new Tg;a.o=this;a.A=a.o.g.entries();a.B=a.A.next();return a};Kg.prototype.v=["java.util.InternalStringMap",0];function Wg(){this.j=0}u(Wg,Hg);function ag(){var a=new Wg;Ig(a);return a}Wg.prototype.v=["java.util.HashMap",0];function $f(){}u($f,cg);q=$f.prototype;q.add=function(a){return this.g.W(a,this)==null};q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ja(a)};q.F=function(){return this.g.Ua().F()};q.remove=function(a){return this.g.remove(a)!=null};q.size=function(){return this.g.size()};q.pa=!0;q.v=["java.util.HashSet",0];function Xg(){}var Yg;u(Xg,I);function Zg(a){var b=new Xg;b.g=a;return b}Xg.prototype.equals=function(a){if(J(a,this))return!0;if(!$g(a))return!1;a=O(a,$g,Xg);return T(this.g,a.g)};Xg.prototype.S=function(){return kf(this.g)};Xg.prototype.toString=function(){return this.g!=null?"Optional.of("+K(K(this.g))+")":"Optional.empty()"};function ah(){ah=k();Yg=Zg(null)}function $g(a){return a instanceof Xg}Xg.prototype.v=["java.util.Optional",0];function pf(a,b){var c=a.size();b.length<c&&(b=Jf(Array(c),b));var d=b;a=a.F();for(var e=0;e<c;e=e+1|0)ze(d,e,a.j());b.length>c&&ze(b,c,null);return b};function Df(){this.B=this.o=0}u(Df,I);Df.prototype.g=function(){return this.o<this.A.size()};Df.prototype.j=function(){Of(this.g());var a;return this.A.Z(this.B=(a=this.o,this.o=this.o+1|0,a))};Df.prototype.l=function(){Pf(this.B!=-1);this.A.Ia(this.B);this.o=this.B;this.B=-1};Df.prototype.v=["java.util.AbstractList$IteratorImpl",0];function Ef(){Df.call(this)}u(Ef,Df);Ef.prototype.v=["java.util.AbstractList$ListIteratorImpl",0];function Dg(){}u(Dg,Bf);q=Dg.prototype;q.contains=function(a){return this.indexOf(a)!=-1};q.Z=function(a){var b=this.size();Hf(a,b);return this.g[a]};q.size=function(){return this.g.length};q.fa=function(){return this.ma(Array(this.g.length))};q.F=function(){var a=new bh;a.B=this.g;return a};q.ma=function(a){var b=this.g.length;a.length<b&&(a=Jf(Array(b),a));for(var c=0;c<b;c=c+1|0)ze(a,c,this.g[c]);a.length>b&&ze(a,b,null);return a};q.v=["java.util.Arrays$ArrayList",0];function bh(){this.o=0}u(bh,I);bh.prototype.g=function(){return this.o<this.B.length};bh.prototype.j=function(){Of(this.g());var a;return this.B[a=this.o,this.o=this.o+1|0,a]};bh.prototype.l=function(){throw jf().g;};bh.prototype.v=["javaemul.internal.ArrayIterator",0];function ch(a,b){if(J(a,b))return!0;if(a==null||b==null||a.length!=b.length)return!1;for(var c=0;c<a.length;c=c+1|0)if(!T(a[c],b[c]))return!1;return!0}function dh(a){if(a==null)return 0;for(var b=1,c=0;c<a.length;c++)b=Math.imul(31,b)+kf(a[c])|0;return b};function eh(){}u(eh,Ee);eh.prototype.v=["java.lang.NumberFormatException",0];function Mf(a,b,c,d,e){var f=a.length,g=c.length;if(b<0||d<0||e<0||(b+e|0)>f||(d+e|0)>g)throw te().g;if(e!=0)if(J(a,c)&&b<d)for(b=b+e|0,e=d+e|0;e>d;)ze(c,e=e-1|0,a[b=b-1|0]);else for(e=d+e|0;d<e;)g=f=void 0,ze(c,(f=d,d=d+1|0,f),a[g=b,b=b+1|0,g])};function Jf(a,b){a.V=b.V;return a};function Xe(a){var b=new De;ge(b,a);M(b,Error(b));throw b.g;}function Of(a){if(!a)throw Oe().g;}function Pf(a){if(!a)throw He().g;}function S(a){fh(a);return a}function fh(a){if(a==null)throw le().g;return a}function Hf(a,b){if(a<0||a>=b)throw ue("Index: "+a+", Size: "+b).g;}function $e(a,b){if(a<0||a>=b)throw Ke("Index: "+a+", Size: "+b).g;}function Ff(a,b){if(a<0||a>b)throw ue("Index: "+a+", Size: "+b).g;};function ne(a,b,c){return gh(a,hh(b,c,a.length))}function gh(a,b){var c=a[0];if(c==null)return null;var d=new globalThis.Array(c);b&&(d.V=b);if(a.length>1){a=a.slice(1);b=b&&hh(b.ia,b.Ea,b.ha-1);for(var e=0;e<c;e++)d[e]=gh(a,b)}else if(b&&(a=b.ia.Jb,a!==void 0))for(b=0;b<c;b++)d[b]=a;return d}function ih(a){a.V=hh(df,ug,1);return a}
function ze(a,b,c){var d;if(!(d=c==null))a:{if(d=a.V)if(d.ha>1){if(!Nf(c,d.ia,d.Ea,d.ha-1)){d=!1;break a}}else if(c!=null&&!d.Ea(c)){d=!1;break a}d=!0}if(!d)throw a=new Ce,de(a),M(a,Error(a)),a.g;a[b]=c}function Nf(a,b,c,d){if(a==null||!Array.isArray(a))return!1;a=a.V||{ia:I,ha:1};var e=a.ha;return e==d?(d=a.ia,d===b?!0:b&&b.prototype.Ya||d&&d.prototype.Ya?!1:c(d.prototype)):e>d?I==b:!1}function hh(a,b,c){return{ia:a,Ea:b,ha:c}};function df(){}u(df,I);function K(a){return a==null?"null":a.toString()}function jh(a,b){$e(b,a.length+1|0);return a.substr(b)}function kh(a,b,c){var d=a.length;if(b<0||c>d||c<b)throw Ke("fromIndex: "+b+", toIndex: "+c+", length: "+d).g;return a.substr(b,c-b|0)}function ug(a){return"string"===typeof a}df.prototype.v=["java.lang.String",0];function lh(){}var mh,nh;u(lh,I);function oh(){oh=k();nh=new ph;mh=new qh}lh.prototype.v=["java.util.Locale",0];function ph(){}u(ph,lh);ph.prototype.toString=ba("");ph.prototype.v=["java.util.Locale$1",0];function qh(){}u(qh,lh);qh.prototype.toString=ba("unknown");qh.prototype.v=["java.util.Locale$4",0];function rh(a,b){this.g=a;this.j=b}u(rh,I);function L(a,b){var c=b||0;return Ye(a,"$$class/"+c,function(){return new rh(a,c)})}function $d(a){return a.j!=0?K(sh("[",a.j))+K(a.g.prototype.v[1]==3?a.g.prototype.v[2]:"L"+K(a.g.prototype.v[0])+";"):a.g.prototype.v[0]}function th(a){return K(a.g.prototype.v[0])+K(sh("[]",a.j))}function uh(a,b){return jh(a,a.lastIndexOf(b)+1|0)}
rh.prototype.toString=function(){return String(this.j==0&&this.g.prototype.v[1]==1?"interface ":this.j==0&&this.g.prototype.v[1]==3?"":"class ")+K($d(this))};function sh(a,b){for(var c="",d=0;d<b;d=d+1|0)c=K(c)+K(a);return c}rh.prototype.v=["java.lang.Class",0];function oe(){this.g=0}u(oe,I);oe.prototype.equals=function(a){return pe(a)?this.g==a.g&&J(this.o,a.o)&&J(this.l,a.l)&&J(this.j,a.j):!1};oe.prototype.S=function(){var a=[mf(this.g),this.l,this.o,this.j];return dh(a)};oe.prototype.toString=function(){return K(this.l)+"."+K(this.o)+"("+K(this.j!=null?this.j:"Unknown Source")+String(this.g>=0?":"+this.g:"")+")"};function pe(a){return a instanceof oe}oe.prototype.v=["java.lang.StackTraceElement",0];function je(){}function ie(a){return a instanceof Error}je.prototype.v=["Error",0];function he(a,b){if(a instanceof Object)try{a.hb=b,Object.defineProperties(a,{cause:{get:function(){return b.o&&b.o.g}}})}catch(c){}};function vh(a,b){ee(this);this.o=b;this.j=a;fe(this);M(this,Error(this))}u(vh,P);vh.prototype.getMessage=p("j");fa.Object.defineProperties(vh.prototype,{error:{configurable:!0,enumerable:!0,get:function(){var a=Error(),b=this.g;a.fileName=b.fileName;a.lineNumber=b.lineNumber;a.columnNumber=b.columnNumber;a.message=b.message;a.name=b.name;a.stack=b.stack;a.toSource=b.toSource;a.cause=b.cause;for(var c in b)c.indexOf("__java$")!=0&&(a[c]=b[c]);return a}}});
vh.prototype.v=["com.google.apps.docs.xplat.base.XplatException",0];function wh(){}function xh(a){return a instanceof Error}wh.prototype.v=["Error",0];function yh(){var a=a==null?function(c){return Ue(Math.floor(Math.random()*c))}:a;var b=(a(2147483647)>>>0).toString(16);b=K(zh("0",Math.max(0,8-b.length|0)))+K(b);a=(a(2147483647)>>>0).toString(16);return K(a)+K(b)};function Ah(){}function Dh(a){return a instanceof Array}Ah.prototype.v=["Array",0];function Eh(){}function Fh(a){return a instanceof Object}Eh.prototype.v=["Object",0];var zh=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};function Gh(){}function W(a){return new RegExp(a,"")}function Hh(a){return a instanceof RegExp}Gh.prototype.v=["RegExp",0];function Ih(){}u(Ih,I);function Jh(a,b){var c=new Ih;if(b==null)throw le().g;c.j=O(b,ug,df);b="g";a.multiline&&(b=K(b)+"m");a.ignoreCase&&(b=K(b)+"i");c.l=new RegExp(a.source,b);return c}function Kh(a){a.g=a.l.exec(a.j);return a.g!=null}Ih.prototype.v=["com.google.apps.xplat.regex.RegExpMatcher",0];function Lh(){}function Mh(a){return a instanceof Object}Lh.prototype.v=["Object",0];var Nh={fc:"build-label",Kb:"buildLabel",Lb:"clientLog",Pb:"docId",jc:"mobile-app-version",tc:"severity",yc:"severity-unprefixed",ac:"isArrayPrototypeIntact",bc:"isEditorElementAttached",Ub:"documentCharacterSet",dc:"isModuleLoadFailure",rc:"reportName",hc:"locale",Nb:"createdOnServer",nc:"numUnsavedCommands",Ob:"cspViolationContext",qc:"relatedToBrowserExtension",zc:"workerError",Qb:"docosPostLimitExceeded",Rb:"docosPostLimitType",Sb:"docosReactionLimitExceeded",Tb:"docosReactionLimitType",sc:"saveTakingTooLongOnClient",
vc:"truncatedCommentNotificationsCount",wc:"truncatedCommentNotificationsFromPayload",mc:"nonfatalReason"};function Oh(){this.g=!1}u(Oh,I);q=Oh.prototype;q.dispose=function(){this.g||(this.g=!0,this.ya(),uh(uh(th(L(ae(this))),"."),"$"))};q.wa=p("g");q.ya=function(){if(this.o!=null){for(var a=this.o,b=0;b<a.length;b++)a[b].dispose();this.o.length=0}};q.toString=function(){return I.prototype.toString.call(this)||""};q.v=["com.google.apps.xplat.disposable.Disposable",0];/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ph=pa([""]),Qh=qa(["\x00"],["\\0"]),Rh=qa(["\n"],["\\n"]),Sh=qa(["\x00"],["\\u0000"]);function Th(a){return a.toString().indexOf("`")===-1}Th(function(a){return a(Ph)})||Th(function(a){return a(Qh)})||Th(function(a){return a(Rh)})||Th(function(a){return a(Sh)});function Uh(a){var b=y.onerror;y.onerror=function(c,d,e,f,g){b&&b(c,d,e,f,g);a({message:c,fileName:d,line:e,lineNumber:e,Bc:f,error:g});return!0}}
function Vh(a){var b=za("window.location.href");a==null&&(a='Unknown Error of type "null/undefined"');if(typeof a==="string")return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:b,stack:"Not available"};var c=!1;try{var d=a.lineNumber||a.line||"Not available"}catch(f){d="Not available",c=!0}try{var e=a.fileName||a.filename||a.sourceURL||y.$googDebugFname||b}catch(f){e="Not available",c=!0}b=Wh(a);return!c&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?{message:a.message,
name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:b}:(c=a.message,c==null&&(c=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:Xh(a.constructor))+'"':"Unknown Error of unknown type",typeof a.toString==="function"&&Object.prototype.toString!==a.toString&&(c+=": "+a.toString())),{message:c,name:a.name||"UnknownError",lineNumber:d,fileName:e,stack:b||"Not available"})}
function Wh(a,b){b||(b={});b[Yh(a)]=!0;var c=a.stack||"",d=a.cause;d&&!b[Yh(d)]&&(c+="\nCaused by: ",d.stack&&d.stack.indexOf(d.toString())==0||(c+=typeof d==="string"?d:d.message+"\n"),c+=Wh(d,b));a=a.errors;if(Array.isArray(a)){d=1;var e;for(e=0;e<a.length&&!(d>4);e++)b[Yh(a[e])]||(c+="\nInner error "+d++ +": ",a[e].stack&&a[e].stack.indexOf(a[e].toString())==0||(c+=typeof a[e]==="string"?a[e]:a[e].message+"\n"),c+=Wh(a[e],b));e<a.length&&(c+="\n... "+(a.length-e)+" more inner errors")}return c}
function Yh(a){var b="";typeof a.toString==="function"&&(b=""+a);return b+a.stack}function Zh(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,Zh));a.stack||(a.stack=$h(Zh));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function ai(a,b){a=Zh(a);if(b)for(var c in b)ob(a,c,b[c]);return a}
function $h(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||$h),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=bi(a||arguments.callee.caller,[]));return b}
function bi(a,b){var c=[];if(bb(b,a)>=0)c.push("[...circular reference...]");else if(a&&b.length<50){c.push(Xh(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=Xh(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(bi(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function Xh(a){if(ci[a])return ci[a];a=String(a);if(!ci[a]){var b=/function\s+([^\(]+)/m.exec(a);ci[a]=b?b[1]:"[Anonymous]"}return ci[a]}var ci={};function di(a,b){this.name=a;this.value=b}di.prototype.toString=p("name");var ei=new di("SEVERE",1E3),fi=new di("WARNING",900),gi=new di("CONFIG",700);function hi(){this.clear()}var ii;function ji(a){var b=ki(),c=b.g;if(c[0]){var d=b.j;b=b.l?d:-1;do b=(b+1)%0,a(c[b]);while(b!==d)}}hi.prototype.clear=function(){this.g=[];this.j=-1;this.l=!1};function ki(){ii||(ii=new hi);return ii};var li=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function mi(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function ni(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]}function oi(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)oi(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))}function pi(a,b){var c=[];for(b=b||0;b<a.length;b+=2)oi(a[b],a[b+1],c);return c.join("&")}
function qi(a){var b=[],c;for(c in a)oi(c,a[c],b);return b.join("&")}function ri(a,b){var c=arguments.length==2?pi(arguments[1],0):pi(arguments,1);return ni(a,c)};function si(a){a&&typeof a.dispose=="function"&&a.dispose()};function ti(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Ba(d)?ti.apply(null,d):si(d)}};function X(){this.G=this.G;this.B=this.B}X.prototype.G=!1;X.prototype.wa=p("G");X.prototype.dispose=function(){this.G||(this.G=!0,this.M())};X.prototype[Symbol.dispose]=function(){this.dispose()};function ui(a,b){b=Ia(si,b);a.G?b():(a.B||(a.B=[]),a.B.push(b))}X.prototype.M=function(){if(this.B)for(;this.B.length;)this.B.shift()()};var vi=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:aa();function wi(a,b){this.l=a;this.o=b;this.j=0;this.g=null}wi.prototype.get=function(){if(this.j>0){this.j--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};function xi(a,b){a.o(b);a.j<100&&(a.j++,b.next=a.g,a.g=b)};var yi=[],zi=[],Ai=!1;function Bi(a){yi[yi.length]=a;if(Ai)for(var b=0;b<zi.length;b++)a(Ha(zi[b].g,zi[b]))};Bi(k());function Ci(){this.j=this.g=null}Ci.prototype.add=function(a,b){var c=Di.get();c.set(a,b);this.j?this.j.next=c:this.g=c;this.j=c};Ci.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.j=null),a.next=null);return a};var Di=new wi(function(){return new Ei},function(a){return a.reset()});function Ei(){this.next=this.scope=this.g=null}Ei.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};Ei.prototype.reset=function(){this.next=this.scope=this.g=null};var Fi,Gi=!1,Hi=new Ci;function Ii(a,b){Fi||Ji();Gi||(Fi(),Gi=!0);Hi.add(a,b)}function Ji(){var a=Promise.resolve(void 0);Fi=function(){a.then(Ki)}}function Ki(){for(var a;a=Hi.remove();){try{a.g.call(a.scope)}catch(b){Ma(b)}xi(Di,a)}Gi=!1};function Li(){};function Mi(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function Y(a){this.g=0;this.G=void 0;this.o=this.j=this.l=null;this.B=this.A=!1;if(a!=Li)try{var b=this;a.call(void 0,function(c){Ni(b,2,c)},function(c){Ni(b,3,c)})}catch(c){Ni(this,3,c)}}function Oi(){this.next=this.l=this.j=this.o=this.g=null;this.B=!1}Oi.prototype.reset=function(){this.l=this.j=this.o=this.g=null;this.B=!1};var Pi=new wi(function(){return new Oi},function(a){a.reset()});function Qi(a,b,c){var d=Pi.get();d.o=a;d.j=b;d.l=c;return d}
function Ri(a){if(a instanceof Y)return a;var b=new Y(Li);Ni(b,2,a);return b}function Si(){var a=Error("Requests cancelled because user has been opted out");return new Y(function(b,c){c(a)})}function Ti(a,b,c){Ui(a,b,c,null)||Ii(Ia(b,a))}function Vi(){var a=[Wi(),Xi()];return new Y(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},g=function(m){c(m)},h,l=0;l<a.length;l++)h=a[l],Ti(h,Ia(f,l),g);else b(e)})}
function Yi(){var a,b,c=new Y(function(d,e){a=d;b=e});return new Zi(c,a,b)}Y.prototype.then=function(a,b,c){return $i(this,vi(typeof a==="function"?a:null),vi(typeof b==="function"?b:null),c)};Y.prototype.$goog_Thenable=!0;q=Y.prototype;q.da=function(a,b){return $i(this,null,vi(a),b)};q.catch=Y.prototype.da;q.cancel=function(a){if(this.g==0){var b=new aj(a);Ii(function(){bj(this,b)},this)}};
function bj(a,b){if(a.g==0)if(a.l){var c=a.l;if(c.j){for(var d=0,e=null,f=null,g=c.j;g&&(g.B||(d++,g.g==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.g==0&&d==1?bj(c,b):(f?(d=f,d.next==c.o&&(c.o=d),d.next=d.next.next):cj(c),dj(c,e,3,b)))}a.l=null}else Ni(a,3,b)}function ej(a,b){a.j||a.g!=2&&a.g!=3||fj(a);a.o?a.o.next=b:a.j=b;a.o=b}
function $i(a,b,c,d){var e=Qi(null,null,null);e.g=new Y(function(f,g){e.o=b?function(h){try{var l=b.call(d,h);f(l)}catch(m){g(m)}}:f;e.j=c?function(h){try{var l=c.call(d,h);l===void 0&&h instanceof aj?g(h):f(l)}catch(m){g(m)}}:g});e.g.l=a;ej(a,e);return e.g}q.Gb=function(a){this.g=0;Ni(this,2,a)};q.Hb=function(a){this.g=0;Ni(this,3,a)};
function Ni(a,b,c){a.g==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,Ui(c,a.Gb,a.Hb,a)||(a.G=c,a.g=b,a.l=null,fj(a),b!=3||c instanceof aj||gj(a,c)))}function Ui(a,b,c,d){if(a instanceof Y)return ej(a,Qi(b||Li,c||null,d)),!0;if(Mi(a))return a.then(b,c,d),!0;if(Ca(a))try{var e=a.then;if(typeof e==="function")return hj(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1}
function hj(a,b,c,d,e){function f(l){h||(h=!0,d.call(e,l))}function g(l){h||(h=!0,c.call(e,l))}var h=!1;try{b.call(a,g,f)}catch(l){f(l)}}function fj(a){a.A||(a.A=!0,Ii(a.qb,a))}function cj(a){var b=null;a.j&&(b=a.j,a.j=b.next,b.next=null);a.j||(a.o=null);return b}q.qb=function(){for(var a;a=cj(this);)dj(this,a,this.g,this.G);this.A=!1};
function dj(a,b,c,d){if(c==3&&b.j&&!b.B)for(;a&&a.B;a=a.l)a.B=!1;if(b.g)b.g.l=null,ij(b,c,d);else try{b.B?b.o.call(b.l):ij(b,c,d)}catch(e){jj.call(null,e)}xi(Pi,b)}function ij(a,b,c){b==2?a.o.call(a.l,c):a.j&&a.j.call(a.l,c)}function gj(a,b){a.B=!0;Ii(function(){a.B&&jj.call(null,b)})}var jj=Ma;function aj(a){La.call(this,a);this.g=!1}z(aj,La);aj.prototype.name="cancel";function Zi(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function kj(){this.B=[];this.o=this.l=!1;this.j=void 0;this.H=this.L=this.G=!1;this.A=0;this.g=null;this.C=0}kj.prototype.cancel=function(a){if(this.l)this.j instanceof kj&&this.j.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.C--,b.C<=0&&b.cancel())}this.H=!0;this.l||(a=new lj(this),mj(this),nj(this,!1,a))}};kj.prototype.J=function(a,b){this.G=!1;nj(this,a,b)};function nj(a,b,c){a.l=!0;a.j=c;a.o=!b;oj(a)}function mj(a){if(a.l){if(!a.H)throw new pj(a);a.H=!1}}
function qj(a){throw a;}function rj(a,b,c){return sj(a,b,null,c)}function tj(a,b,c){sj(a,b,function(d){var e=b.call(this,d);if(e===void 0)throw d;return e},c)}function sj(a,b,c,d){var e=a.l;e||(b===c?b=c=vi(b):(b=vi(b),c=vi(c)));a.B.push([b,c,d]);e&&oj(a);return a}kj.prototype.then=function(a,b,c){var d,e,f=new Y(function(g,h){e=g;d=h});sj(this,e,function(g){g instanceof lj?f.cancel():d(g);return uj},this);return f.then(a,b,c)};kj.prototype.$goog_Thenable=!0;
function vj(a){return cb(a.B,function(b){return typeof b[1]==="function"})}var uj={};
function oj(a){if(a.A&&a.l&&vj(a)){var b=a.A,c=wj[b];c&&(y.clearTimeout(c.g),delete wj[b]);a.A=0}a.g&&(a.g.C--,delete a.g);b=a.j;for(var d=c=!1;a.B.length&&!a.G;){var e=a.B.shift(),f=e[0],g=e[1];e=e[2];if(f=a.o?g:f)try{var h=f.call(e||null,b);h===uj&&(h=void 0);h!==void 0&&(a.o=a.o&&(h==b||h instanceof Error),a.j=b=h);if(Mi(b)||typeof y.Promise==="function"&&b instanceof y.Promise)d=!0,a.G=!0}catch(l){b=l,a.o=!0,vj(a)||(c=!0)}}a.j=b;d&&(h=Ha(a.J,a,!0),d=Ha(a.J,a,!1),b instanceof kj?(sj(b,h,d),b.L=
!0):b.then(h,d));c&&(b=new xj(b),wj[b.g]=b,a.A=b.g)}function yj(a){var b=new kj;mj(b);nj(b,!0,a);return b}function pj(){La.call(this)}z(pj,La);pj.prototype.message="Deferred has already fired";pj.prototype.name="AlreadyCalledError";function lj(){La.call(this)}z(lj,La);lj.prototype.message="Deferred was canceled";lj.prototype.name="CanceledError";function xj(a){this.g=y.setTimeout(Ha(this.l,this),0);this.j=a}xj.prototype.l=function(){delete wj[this.g];qj(this.j)};var wj={};function zj(){}function Aj(a){return a!=null&&!!a.Ja}zj.prototype.Ja=!0;zj.prototype.v=["com.google.apps.docs.xplat.flag.FlagService",1];var Bj;function Cj(){if(Bj==null){var a=new Dj(null);Bj=function(){return a}}var b;return O((b=Bj,b()),Aj,zj)};function Ej(){}u(Ej,I);Ej.prototype.get=function(){if(this.j==null){var a=O(y._docs_flag_initialData,Fh,Eh);this.j=a!=null?a:O({},Fh,Eh)}return this.j};Ej.prototype.g=function(){return this.get()};Ej.prototype.v=["com.google.apps.docs.xplat.flag.FlagServiceHelper",0];function Fj(a){return typeof a=="string"?a=="true"||a=="1":!!a};function Dj(a){this.g=new Ej;if(a!=null)for(var b in a){var c=b,d=a[b],e=O(this.g.g(),Fh,Eh);ye(d)?(d=O(d,ye,xe).ca,e[c]=d):e[c]=d!=null?d:null}}u(Dj,I);Dj.prototype.clear=function(){this.g=new Ej};Dj.prototype.get=function(a){return O(this.g.g(),Fh,Eh)[a]};function Gj(a,b){a=O(a.g.g(),Fh,Eh);return b in a}
function Hj(a,b){if(!Gj(a,b)||a.get(b)==null)return NaN;try{var c=K(a.get(b));Qe==null&&(Qe=RegExp("^\\s*[+-]?(NaN|Infinity|((\\d+\\.?\\d*)|(\\.\\d+))([eE][+-]?\\d+)?[dDfF]?)\\s*$"));if(!Qe.test(c)){var d=new eh;ge(d,'For input string: "'+K(c)+'"');M(d,Error(d));throw d.g;}return parseFloat(c)}catch(f){var e=ke(f);if(e instanceof eh)return NaN;throw e.g;}}
function Ij(a,b){if(!Gj(a,b))return"";a=a.get(b);if(a==null)var c="";else{if(b="number"===typeof a){b=Se(S(a));var d=Se(S(a));b=b.equals(d)}b?c=""+Se(S(a)):c=K(a)}return c}Dj.prototype.Ja=!0;Dj.prototype.v=["com.google.apps.docs.xplat.flag.FlagServiceImpl",0];function Jj(a){vh.call(this,a,null);M(this,Error(this))}u(Jj,vh);Jj.prototype.v=["com.google.apps.docs.xplat.net.LimitException",0];function Kj(a,b,c,d){this.g=!1;this.B=a;this.l=b;this.j=new Lj(Math.imul(c,1E3),d)}u(Kj,Oh);Kj.prototype.v=["com.google.apps.docs.xplat.net.QpsLimiter",0];function Mj(){this.l=this.o=this.g=0}u(Mj,I);function Nj(a){return a instanceof Mj}Mj.prototype.v=["com.google.apps.docs.xplat.util.BasicStat$Slot",0];function Lj(a){this.j=0;this.l=a;this.j=Te(a/50);this.g=new Oj(mf(50))}u(Lj,I);Lj.prototype.get=function(a){return Pj(this,a,function(b,c){b=O(b,ye,xe);c=O(c,Nj,Mj);return mf(b.ca+c.g|0)})};function Pj(a,b,c){b=b!=null?S(b):Jd(Qd(Date.now()));Qj(a,b);var d=0;b=Rj(a,S(b));b=S(b)-a.l;for(var e=a.g.g.length-1|0;e>=0;e=e-1|0){var f=O(a.g.get(e),Nj,Mj);if(S(f.j)<=b)break;d=O(c(mf(d),f),ye,xe).ca}return d}function Rj(a,b){return a.j*Math.floor(b/a.j+1)}
function Qj(a,b){var c=O(Sj(a.g),Nj,Mj);c!=null&&(c=S(c.j)-a.j,S(b)<S(c)&&a.g.clear())}Lj.prototype.v=["com.google.apps.docs.xplat.util.BasicStat",0];function Oj(a){this.j=this.l=0;a!=null?"number"===typeof a?(a=S(a),a=Ue(a)):a=a instanceof Id?S(a).K:a.ca:a=100;this.l=a;this.g=O([],Dh,Ah)}u(Oj,I);q=Oj.prototype;q.add=function(a){var b=this.g[this.j];this.g[this.j]=a;this.j=Te((this.j+1|0)%this.l);return b};q.get=function(a){a=Tj(this,a);return this.g[a]};q.set=function(a,b){a=Tj(this,a);this.g[a]=b};q.clear=function(){this.j=this.g.length=0};
q.Da=function(){for(var a=this.g.length,b=this.g.length-this.g.length|0,c=O([],Dh,Ah);b<a;b=b+1|0){var d=c,e=this.get(b);d.push(e)}return c};function Sj(a){return a.g.length==0?null:a.get(a.g.length-1|0)}function Tj(a,b){if(b>=a.g.length)throw te().g;return a.g.length<a.l?b:Te((a.j+b|0)%a.l)}q.v=["com.google.apps.docs.xplat.util.CircularBuffer",0];function Uj(){this.g=0}var Vj,Wj;u(Uj,I);function Z(a,b){var c=new Uj;c.j=a;c.g=b;Vj[a]=c!==void 0?c:null;return c}Uj.prototype.toString=p("j");
function Xj(){Xj=k();Vj=O({},Mh,Lh);Z("IDLE",1);Z("BUSY",1);Z("RECOVERING",2);Wj=Z("OFFLINE",3);Z("SERVER_DOWN",3);Z("FORBIDDEN",4);Z("AUTH_REQUIRED",4);Z("SESSION_LIMIT_EXCEEDED",5);Z("LOCKED",5);Z("INCOMPATIBLE_SERVER",5);Z("CLIENT_ERROR",5);Z("CLIENT_FATAL_ERROR",5);Z("CLIENT_FATAL_ERROR_PENDING_CHANGES",5);Z("BATCH_CLIENT_ERROR",3);Z("SAVE_ERROR",5);Z("DOCUMENT_TOO_LARGE",5);Z("BATCH_SAVE_ERROR",3);Z("DOCS_EVERYWHERE_IMPORT_ERROR",5);Z("POST_LIMIT_EXCEEDED_ERROR",5);Z("DOCS_QUOTA_EXCEEDED_ERROR",
5)}Uj.prototype.v=["com.google.apps.docs.xplat.net.Status$State",0];function Yj(){}u(Yj,I);function Zj(a){return a instanceof Yj}Yj.prototype.v=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function ak(){this.g=!1;this.j=O([],Dh,Ah)}u(ak,Oh);function bk(a,b,c){var d;a:{for(d=0;d<a.j.length;d=d+1|0){var e=O(a.j[d],Zj,Yj);if(J(e.j,c)&&J(e.g,b)){d=!0;break a}}d=!1}d||(a=a.j,c=b.g(c),d=new Yj,d.g=b,d.j=c,a.push(d))}ak.prototype.ya=function(){this.removeAll();Oh.prototype.ya.call(this)};ak.prototype.removeAll=function(){for(var a=O(this.j.pop(),Zj,Yj);a!=null;)a.g.j(a.j),a=O(this.j.pop(),Zj,Yj)};ak.prototype.v=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];var ck,dk,ek,fk,gk,hk,ik,jk,kk,lk,mk,nk,ok,pk,qk,rk;
function sk(){sk=k();ck=vf();dk=vf();ek=yf(ih("Trusted Type;TrustedHTML;TrustedScript;cannot communicate with background;zaloJSV2;kaspersky-labs;@user-script;Object Not Found Matching Id;contextChanged;Not implemented on this platform;Extension context invalidated;neurosurgeonundergo;realTimeClData;Failed to execute 'querySelectorAll' on 'Document';Promise.all(...).then(...).catch(...).finally is not a function;Error executing Chrome API, chrome.tabs;zotero;enableLTSeparator;Identifier 'originalPrompt' has already been declared;User rejected the request;Could not inject ethereum provider because it's not your default extension;Cannot redefine property: googletag;Can't find variable: HTMLDialogElement;Identifier 'listenerName' has already been declared;Cannot read properties of undefined (reading 'info');Permission denied to access property \"type\";Error: Promise timed out;Request timeout ToolbarStatus;Can't find variable: nc;imtgo;ton is not a function;__renderMessageNode is not defined;Cannot redefine property: ethereum".split(";")));fk=
yf(ih("puppeteer-core;kaspersky-labs;@user-script;jsQuilting;linkbolic;neurosurgeonundergo;tlscdn;https://cdnjs.cloudflare.com/ajax/libs/mathjax/;secured-pixel.com;Can't find variable: nc;imtgo;_simulateEvent".split(";")));gk=vf(W("^_0x[a-f0-9]{6} is not defined$"));hk=yf(ih("egfdjlfmgnehecnclamagfafdccgfndp mndnfokpggljbaajbnioimlmbfngpief mlkejohendkgipaomdopolhpbihbhfnf kgonammgkackdilhodbgbmodpepjocdp klbcgckkldhdhonijdbnhhaiedfkllef pmehocpgjmkenlokgjfkaichfjdhpeol cjlaeehoipngghikfjogbdkpbdgebppb ghbmnnjooekpmoecnnnilnnbdlolhkhi lmjegmlicamnimmfhcmpkclmigmmcbeh gmbmikajjgmnabiglmofipeabaddhgne lpcaedmchfhocbbapmcbpinfpgnhiddi gbkeegbaiigmenfmjfclcdgdpimamgkj adokjfanaflbkibffcbhihgihpgijcei".split(" ")));
ik=vf(W("chrome-extension://([^\\/]+)"),W("moz-extension://([^\\/]+)"),W("ms-browser-extension://([^\\/]+)"),W("webkit-masked-url://([^\\/]+)"),W("safari-web-extension://([^\\/]+)"));jk=yf(ih('status is 0, navigator.onLine =;Network sync is disabled. Aborting a network request of int type;The service is currently unavailable.;Internal error encountered.;A network error occurred and the request could not be completed.;data does not exist in AF cache;There was an error during the transport or processing of this request;Failed to retrieve dependencies of service;Failed to load gapi;Rpc failed due to xhr error. error code: 6, error:  [0];An interceptor has requested that the request be retried;8,"generic";A network error occurred'.split(";")));
kk=vf(W("^Permission denied$"));lk=yf(ih("Kg is not defined;uncaught error;The play method is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.;Illegal invocation;Script error;zCommon;can't access dead object;Java exception was raised during method invocation;pauseVideo is not a function;ResizeObserver loop;wallet must has at least one account;xbrowser is not defined;jQuery is not defined;Cannot read properties of null (reading 'requestAnimationFrame');Class extends value undefined is not a constructor or null;GM3TooltipService: No tooltip with id;Mole was disposed;getInitialTopicListResponse is missing for stream rendering;getPeopleById call preempted;The operation is insecure;class heritage;The play() request was interrupted;args.site.enabledFeatures is undefined".split(";")));
mk=vf(W("phantomjs|node:electron|py-scrap|eval code|Program Files"));nk=vf(W("Script https:\\/\\/meet.google.com\\/.*meetsw.*load failed"),W("A bad HTTP response code \\(\\d+\\) was received when fetching the script"));ok=yf(ih("Service worker registration is disabled by MDA;An unknown error occurred when fetching the script;Operation has been aborted;Timed out while trying to start the Service Worker;The Service Worker system has shutdown;The user denied permission to use Service Worker;The script resource is behind a redirect, which is disallowed;The document is in an invalid state;ServiceWorker script evaluation failed;ServiceWorker cannot be started;Failed to access storage;Worker disallowed;encountered an error during installation".split(";")));
pk=vf(W("Error loading.*Consecutive load failures"),W("Failed to load module.*Consecutive load failures"));qk=vf(W("Error loading.*Consecutive load failures"),W("Failed to load module.*Consecutive load failures"));rk=vf("Timeout reached for loading script https://www.gstatic.com/_/apps-fileview/_/js/","Error while loading script https://www.gstatic.com/_/apps-fileview/_/js/")};function tk(){}u(tk,I);function uk(a){return a instanceof tk}tk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorClassifier",0];function uf(){}u(uf,tk);uf.prototype.B=function(a){a:{a=vk(a);for(var b=!1,c=(sk(),ik).F();c.g();){var d=O(c.j(),Hh,Gh);for(d=Jh(d,a);Kh(d);){b=d;if(b.g==null)throw a=new Ge,ge(a,"No match available"),M(a,Error(a)),a.g;if(1>(b.g.length-1|0))throw ue("No group 1").g;b=O(b.g[1],ug,df);ah();b=b==null?Yg:Zg(fh(b));b=O(b.g!=null?b.g:"",ug,df);if(hk.contains(b)){a=!1;break a}b=!0}}a=b}return a};uf.prototype.v=["com.google.apps.telemetry.xplat.error.BaseExtensionErrorClassifier",0];function wk(){}u(wk,I);wk.prototype.equals=function(a){return xk(this,a)};wk.prototype.S=function(){for(var a=1,b=yk(this),c=0;c<b.length;c++){var d=this[b[c]];d!=null&&(d=d.V?dh(d):cf(d),a=Math.imul(1000003,a)^d)}return a};wk.prototype.toString=function(){var a=We(this);a=uh(uh(th(a),"."),"$");a=jh(a,a.lastIndexOf("AutoValue_")+1|0);a=qf(K(a)+"{","}");for(var b=yk(this),c=0;c<b.length;c++){var d=b[c],e=this[d];Array.isArray(e)&&(e="["+K(e)+"]");rf(a,K(d)+"="+K(e))}return a.toString()};
function xk(a,b){if(b==null||!J(We(b),We(a)))return!1;var c=yk(a);if(c.length!=yk(b).length)return!1;for(var d=0;d<c.length;d++){var e=c[d],f=a[e];e=b[e];if(!(J(f,e)||(f==null||e==null?0:f.V&&e.V?J(We(f),We(e))&&ch(f,e):bf(f,e))))return!1}return!0}wk.prototype.v=["javaemul.internal.ValueType",0];function yk(a){var b=Object.keys(a),c=a.B;return c?b.filter(function(d){return!c.includes(d)}):b};function zk(){}u(zk,wk);zk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorClassification",0];function Ak(){}u(Ak,I);function Bk(a,b){lf(b);a.l=b;return a}function Ck(a,b){lf(b);a.o=b;return a}function Dk(a){if(a.l==null||a.g==null||a.o==null)throw He().g;var b=new Ek,c=a.g,d=a.j,e=a.o;b.l=a.l;b.g=c;b.j=d;b.o=e;return b}Ak.prototype.v=["com.google.apps.telemetry.xplat.error.JsError$Builder",0];function Ek(){}u(Ek,wk);function Fk(a){var b="";a=a.j;a!=null&&(b=K(b)+(K(a.getMessage())+"\n"),b=K(b)+(K(a.g)+"\n"),b=K(b)+K(Fk(a)));return b}function vk(a){return K(a.getMessage())+"\n"+K(a.g)+"\n"+K(Fk(a))}Ek.prototype.getMessage=p("l");Ek.prototype.v=["com.google.apps.telemetry.xplat.error.JsError",0];function Gk(){this.g=!1}var Hk,Ik,Jk,Kk,Lk,Mk,Nk,Ok,Pk,wf,xf;u(Gk,I);
function Qk(a,b,c){if(b==null)return"no throwable";if(c>3)return"max depth reached";var d="",e=b.j!=null?b.j:"no message";if(b.l==null){a:{var f=b.g;if(ie(f)&&(f=O(f,ie,je),f.stack!=null)){var g=f.stack,h=RegExp("\n","g");f=ne([0],df,ug);for(var l=0,m=g,n=null;;){var t=h.exec(m);if(t==null||m===""){ze(f,l,m);break}else{var x=t.index;ze(f,l,kh(m,0,x));m=kh(m,x+t.at(0).length|0,m.length);h.lastIndex=0;J(n,m)&&(ze(f,l,kh(m,0,1)),m=jh(m,1));n=m;l=l+1|0}}if(g.length>0){for(g=f.length;g>0&&f[g-1|0]==="";)g=
g-1|0;g<f.length&&(f.length=g)}g=ne([f.length],oe,pe);for(h=0;h<f.length;h=h+1|0)l=h,n=m=new oe,t=f[h],n.l="",n.o=t,n.j=null,n.g=-1,ze(g,l,m);f=g;break a}f=ne([0],oe,pe)}b.l=f}f=b.l;d=K(d)+(K(e)+"\ntop stack frame: "+K(f!=null&&f.length>0?f[0].toString():"no stack trace"));b=b.o;b!=null&&(d=K(d)+("\ncause: "+K(Qk(a,b,c+1|0))));return d}
function Rk(){Rk=k();wf=Sk((sk(),ek),fk,1);xf=Tk(gk,dk,1);Ik=Sk(jk,ck,2);Kk=Sk(lk,ck,3);Jk=Tk(kk,mk,3);Nk=Tk(pk,qk,4);Ok=Sk(rk,ck,4);Lk=Tk(nk,dk,5);Mk=Sk(ok,ck,5);Hk=tf();Pk=Xf("SEVERE","SEVERE_AFTER_INITIAL","FATAL","UNKNOWN","")}Gk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorProcessor",0];function Uk(){}u(Uk,wk);Uk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorProcessorResult",0];function Vk(){}u(Vk,tk);function Tk(a,b,c){var d=new Vk;d.j=c;d.g=0;d.l=a;d.o=b;return d}Vk.prototype.B=function(a){var b=Fk(a);return Wk(a.getMessage(),this.l)||Wk(a.g,this.o)||Wk(b,this.l)||Wk(b,this.o)};function Wk(a,b){for(b=b.F();b.g();){var c=O(b.j(),Hh,Gh);if(Kh(Jh(c,a)))return!0}return!1}Vk.prototype.v=["com.google.apps.telemetry.xplat.error.RegexErrorClassifier",0];function Xk(){this.o=!1}u(Xk,tk);Xk.prototype.B=function(a){if(this.o)a:{a=a.getMessage();for(var b=0;b<this.l.size();b=b+1|0){var c=a,d=O(this.l.Z(b),ug,df);if(J(S(c),d)){a=!0;break a}}a=!1}else a=vk(a),a=Yk(a,this.l)||Yk(a,this.A);return a};function Yk(a,b){for(var c=0;c<b.size();c=c+1|0){var d=a,e=O(b.Z(c),ug,df);if(d.indexOf(e.toString())!=-1)return!0}return!1}function Sk(a,b,c){var d=new Xk;d.j=c;d.g=0;d.l=a;d.A=b;d.o=!1;return d}
Xk.prototype.v=["com.google.apps.telemetry.xplat.error.StringErrorClassifier",0];function Zk(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function $k(a){var b={},c;for(c in a)b[c]=a[c];return b}var al="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function bl(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<al.length;f++)c=al[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function cl(a){this.g=this.G=this.o="";this.C=null;this.A=this.j="";this.B=!1;var b;a instanceof cl?(this.B=a.B,dl(this,a.o),this.G=a.G,this.g=a.g,el(this,a.C),fl(this,a.j),gl(this,a.l.clone()),this.A=a.A):a&&(b=String(a).match(li))?(this.B=!1,dl(this,b[1]||"",!0),this.G=hl(b[2]||""),this.g=hl(b[3]||"",!0),el(this,b[4]),fl(this,b[5]||"",!0),gl(this,b[6]||"",!0),this.A=hl(b[7]||"")):(this.B=!1,this.l=new il(null,this.B))}
cl.prototype.toString=function(){var a=[],b=this.o;b&&a.push(jl(b,kl,!0),":");var c=this.g;if(c||b=="file")a.push("//"),(b=this.G)&&a.push(jl(b,kl,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.C,c!=null&&a.push(":",String(c));if(c=this.j)this.g&&c.charAt(0)!="/"&&a.push("/"),a.push(jl(c,c.charAt(0)=="/"?ll:ml,!0));(c=this.l.toString())&&a.push("?",c);(c=this.A)&&a.push("#",jl(c,nl));return a.join("")};
cl.prototype.resolve=function(a){var b=this.clone(),c=!!a.o;c?dl(b,a.o):c=!!a.G;c?b.G=a.G:c=!!a.g;c?b.g=a.g:c=a.C!=null;var d=a.j;if(c)el(b,a.C);else if(c=!!a.j){if(d.charAt(0)!="/")if(this.g&&!this.j)d="/"+d;else{var e=b.j.lastIndexOf("/");e!=-1&&(d=b.j.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):h==".."?((f.length>1||f.length==1&&
f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?fl(b,d):c=a.l.toString()!=="";c?gl(b,a.l.clone()):c=!!a.A;c&&(b.A=a.A);return b};cl.prototype.clone=function(){return new cl(this)};function dl(a,b,c){a.o=c?hl(b,!0):b;a.o&&(a.o=a.o.replace(/:$/,""));return a}function el(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.C=b}else a.C=null}function fl(a,b,c){a.j=c?hl(b,!0):b;return a}
function gl(a,b,c){b instanceof il?(a.l=b,ol(a.l,a.B)):(c||(b=jl(b,pl)),a.l=new il(b,a.B))}function ql(a,b,c){a.l.set(b,c);return a}function hl(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function jl(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,rl),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function rl(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var kl=/[#\/\?@]/g,ml=/[#\?:]/g,ll=/[#\?]/g,pl=/[#\?@]/g,nl=/#/g;
function il(a,b){this.j=this.g=null;this.l=a||null;this.o=!!b}function sl(a){a.g||(a.g=new Map,a.j=0,a.l&&mi(a.l,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}q=il.prototype;q.add=function(a,b){sl(this);this.l=null;a=tl(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.j=this.j+1;return this};q.remove=function(a){sl(this);a=tl(this,a);return this.g.has(a)?(this.l=null,this.j=this.j-this.g.get(a).length,this.g.delete(a)):!1};
q.clear=function(){this.g=this.l=null;this.j=0};function ul(a,b){sl(a);b=tl(a,b);return a.g.has(b)}q.forEach=function(a,b){sl(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};q.Da=function(a){sl(this);var b=[];if(typeof a==="string")ul(this,a)&&(b=b.concat(this.g.get(tl(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
q.set=function(a,b){sl(this);this.l=null;a=tl(this,a);ul(this,a)&&(this.j=this.j-this.g.get(a).length);this.g.set(a,[b]);this.j=this.j+1;return this};q.get=function(a,b){if(!a)return b;a=this.Da(a);return a.length>0?String(a[0]):b};
q.toString=function(){if(this.l)return this.l;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Da(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.l=a.join("&")};q.clone=function(){var a=new il;a.l=this.l;this.g&&(a.g=new Map(this.g),a.j=this.j);return a};function tl(a,b){b=String(b);a.o&&(b=b.toLowerCase());return b}
function ol(a,b){b&&!a.o&&(sl(a),a.l=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(this.remove(d),this.remove(e),c.length>0)){this.l=null;d=this.g;var f=d.set;e=tl(this,e);var g=c.length;if(g>0){for(var h=Array(g),l=0;l<g;l++)h[l]=c[l];g=h}else g=[];f.call(d,e,g);this.j=this.j+c.length}},a));a.o=b};function vl(){var a=y.window;a.onbeforeunload=k();a.location.reload()};function wl(){this.g=function(){vl()}}wl.prototype.notify=function(){window.confirm("This error has been reported to Google and we'll look into it as soon as possible. Please reload this page to continue.")&&this.g()};function xl(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.na=!1}xl.prototype.stopPropagation=function(){this.na=!0};xl.prototype.preventDefault=function(){this.defaultPrevented=!0};var yl=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=k();y.addEventListener("test",c,b);y.removeEventListener("test",c,b)}catch(d){}return a}();function zl(a,b){xl.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.g=null;a&&this.init(a,b)}z(zl,xl);
zl.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=a.offsetX,this.offsetY=a.offsetY,this.clientX=
a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.g=a;a.defaultPrevented&&zl.ba.preventDefault.call(this)};
zl.prototype.stopPropagation=function(){zl.ba.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};zl.prototype.preventDefault=function(){zl.ba.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var Al="closure_listenable_"+(Math.random()*1E6|0);var Bl=0;function Cl(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.va=e;this.key=++Bl;this.removed=this.sa=!1}function Dl(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.va=null};function El(a){this.src=a;this.g={};this.j=0}El.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.j++);var g=Fl(a,b,d,e);g>-1?(b=a[g],c||(b.sa=!1)):(b=new Cl(b,this.src,f,!!d,e),b.sa=c,a.push(b));return b};El.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=Fl(e,b,c,d);return b>-1?(Dl(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.g[a],this.j--),!0):!1};
function Gl(a,b){var c=b.type;c in a.g&&db(a.g[c],b)&&(Dl(b),a.g[c].length==0&&(delete a.g[c],a.j--))}El.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,Dl(d[e]);delete this.g[c];this.j--}return b};function Fl(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.va==d)return e}return-1};var Hl="closure_lm_"+(Math.random()*1E6|0),Il={},Jl=0;function Kl(a,b,c,d,e){if(d&&d.once)return Ll(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Kl(a,b[f],c,d,e);return null}c=Ml(c);return a&&a[Al]?a.listen(b,c,Ca(d)?!!d.capture:!!d,e):Nl(a,b,c,!1,d,e)}
function Nl(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Ca(e)?!!e.capture:!!e,h=Ol(a);h||(a[Hl]=h=new El(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Pl();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)yl||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Sl(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Jl++;return c}
function Pl(){function a(c){return b.call(a.src,a.listener,c)}var b=Tl;return a}function Ll(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Ll(a,b[f],c,d,e);return null}c=Ml(c);return a&&a[Al]?a.j.add(String(b),c,!0,Ca(d)?!!d.capture:!!d,e):Nl(a,b,c,!0,d,e)}
function Ul(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ul(a,b[f],c,d,e);else(d=Ca(d)?!!d.capture:!!d,c=Ml(c),a&&a[Al])?a.j.remove(String(b),c,d,e):a&&(a=Ol(a))&&(b=a.g[b.toString()],a=-1,b&&(a=Fl(b,c,d,e)),(c=a>-1?b[a]:null)&&Vl(c))}
function Vl(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[Al])Gl(b.j,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Sl(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Jl--;(c=Ol(b))?(Gl(c,a),c.j==0&&(c.src=null,b[Hl]=null)):Dl(a)}}}function Sl(a){return a in Il?Il[a]:Il[a]="on"+a}
function Tl(a,b){if(a.removed)a=!0;else{b=new zl(b,this);var c=a.listener,d=a.va||a.src;a.sa&&Vl(a);a=c.call(d,b)}return a}function Ol(a){a=a[Hl];return a instanceof El?a:null}var Wl="__closure_events_fn_"+(Math.random()*1E9>>>0);function Ml(a){if(typeof a==="function")return a;a[Wl]||(a[Wl]=function(b){return a.handleEvent(b)});return a[Wl]}Bi(function(a){Tl=a(Tl)});function Xl(a,b){xl.call(this,a);this.error=b}u(Xl,xl);var Yl=/\/d\/([^\/]+)/,Zl=/\/r\/([^\/]+)/;function $l(a){a=a.match(li)[5]||null;return Yl.test(a)}function am(a,b){if($l(a)){$l(a);var c=a.match(li),d=c[5];d=d.replace(b,"");b=c[1];a=c[2];var e=c[3],f=c[4],g=c[6];c=c[7];var h="";b&&(h+=b+":");e&&(h+="//",a&&(h+=a+"@"),h+=e,f&&(h+=":"+f));d&&(h+=d);g&&(h+="?"+g);c&&(h+="#"+c);b=h}else b=a;return b};function bm(){X.call(this);this.j=new El(this);this.Za=this;this.U=null}z(bm,X);bm.prototype[Al]=!0;q=bm.prototype;q.addEventListener=function(a,b,c,d){Kl(this,a,b,c,d)};q.removeEventListener=function(a,b,c,d){Ul(this,a,b,c,d)};
q.dispatchEvent=function(a){var b=this.U;if(b){var c=[];for(var d=1;b;b=b.U)c.push(b),++d}b=this.Za;d=a.type||a;if(typeof a==="string")a=new xl(a,b);else if(a instanceof xl)a.target=a.target||b;else{var e=a;a=new xl(d,b);bl(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.na&&f>=0;f--){var g=a.currentTarget=c[f];e=cm(g,d,!0,a)&&e}a.na||(g=a.currentTarget=b,e=cm(g,d,!0,a)&&e,a.na||(e=cm(g,d,!1,a)&&e));if(c)for(f=0;!a.na&&f<c.length;f++)g=a.currentTarget=c[f],e=cm(g,d,!1,a)&&e;return e};
q.M=function(){bm.ba.M.call(this);this.j&&this.j.removeAll(void 0);this.U=null};q.listen=function(a,b,c,d){return this.j.add(String(a),b,!1,c,d)};function cm(a,b,c,d){b=a.j.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,l=g.va||g.src;g.sa&&Gl(a.j,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};function dm(a,b,c){if(typeof a==="function")c&&(a=Ha(a,c));else if(a&&typeof a.handleEvent=="function")a=Ha(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:y.setTimeout(a,b||0)}function em(){var a=null;return(new Y(function(b,c){a=dm(function(){b(void 0)},2E3);a==-1&&c(Error("Failed to schedule timer."))})).da(function(b){y.clearTimeout(a);throw b;})};function fm(a,b,c){X.call(this);this.g=a;this.l=b||0;this.j=c;this.o=Ha(this.ob,this)}z(fm,X);q=fm.prototype;q.ka=0;q.M=function(){fm.ba.M.call(this);this.stop();delete this.g;delete this.j};q.start=function(a){this.stop();this.ka=dm(this.o,a!==void 0?a:this.l)};q.stop=function(){this.isActive()&&y.clearTimeout(this.ka);this.ka=0};q.isActive=function(){return this.ka!=0};q.ob=function(){this.ka=0;this.g&&this.g.call(this.j)};function gm(a,b,c,d){X.call(this);this.l=d!=null?d:.15;this.A=a;this.o=b;this.H=c;this.g=new fm(this.Db,void 0,this);this.C=Number.NEGATIVE_INFINITY;this.j=0}u(gm,X);q=gm.prototype;q.isActive=function(){return this.g.isActive()};q.start=function(){hm(this,!1,!1)};function hm(a,b,c){b&&(a.g.stop(),im(a,a.o));a.isActive()||(b=Math.max(0,a.C+a.j-Date.now()),b==0&&(c?b=im(a,a.o):a.j=0),a.g.start(b))}q.stop=function(){this.g.stop()};
function im(a,b){b>0&&a.l!=0&&(b=Math.floor(b*(1-a.l+Math.random()*a.l*2)));return a.j=b}q.Db=function(){this.C=Date.now();im(this,Math.min(Math.max(this.j*2,this.o),this.H));this.A()};q.M=function(){this.g.dispose();delete this.g;delete this.A;X.prototype.M.call(this)};function jm(a){X.call(this);this.j=a;this.g={}}z(jm,X);var km=[];jm.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(km[0]=b.toString()),b=km);for(var e=0;e<b.length;e++){var f=Kl(a,b[e],c||this.handleEvent,d||!1,this.j||this);if(!f)break;this.g[f.key]=f}return this};jm.prototype.removeAll=function(){Zk(this.g,function(a,b){this.g.hasOwnProperty(b)&&Vl(a)},this);this.g={}};jm.prototype.M=function(){jm.ba.M.call(this);this.removeAll()};
jm.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function lm(a,b,c,d,e,f,g){g=g===void 0?!0:g;X.call(this);var h=this;this.g=a;this.g.P=1E4;this.ga=b;this.l=f;this.j=new gm(function(){return h.Ga()},3E4,36E5);this.A=0;this.C=null;this.X=new Kj("errorsender",1,8,d);ui(this,this.X);this.U=!1;this.J=null;this.N=new Set;this.H=new jm(this);this.qa=c||10;this.Y=e||null;this.H.listen(this.g,"complete",this.wb);this.H.listen(this.g,"ready",this.Ga);this.T=null;this.L=new ak;ui(this,this.L);this.l&&bk(this.L,this.l.j(),function(){h.l.getState().g>=3&&(h.T=
(Xj(),Wj));h.l.getState().g>=3||h.T!==(Xj(),Wj)||mm(h)});this.P=g}u(lm,X);q=lm.prototype;q.send=function(a,b,c,d){Fj(this.ga.get("docs-dafjera"))&&(a=am(am(a,Zl),Yl));var e=rj(rj(yj(this.o.length),function(f){if(!(f>=this.qa))return this.P&&(a=ri(a,"errorSender_enqueueTimeMs",Date.now().toString())),f={},f.u=a,f.m=b,f.c=c,f.h=d,this.enqueue(f)},this),this.Ga,this);tj(e,function(){this.N.delete(e)},this);this.N.add(e)};
q.Ga=function(){var a=this.l&&this.l.getState().g>=3,b=this.wa()||this.g.isActive()||this.j.isActive()||this.U;return a||b?yj():nm(this)};function nm(a){return function(){return rj(yj(a.o[0]!==void 0?a.o[0]:null),function(b){return om(a,b)})}()}
function om(a,b){if(a.j.isActive()||a.g.isActive()||a.U)return yj();if(!b)return a.j.stop(),yj();if(b.u.length>4E3)return pm(a);try{var c=a.X;if(!((c.j.get(null)+1|0)/S(c.j.l/1E3)<=c.l))throw(new Jj("Query would cause "+K(c.B)+" to exceed "+c.l+" qps.")).g;var d=c.j,e=Jd(Qd(Date.now()));Qj(d,e);var f=O(Sj(d.g),Nj,Mj);if(f==null||S(e)>=S(f.j)){var g=Rj(d,S(e)),h=new Mj;h.j=g;h.g=0;h.o=2147483647;h.l=-2147483648;f=h;d.g.add(f)}f.g=f.g+1|0;f.o=Math.min(1,f.o);f.l=Math.max(1,f.l);a.J=new kj;var l=b.u;
a.Y!=null&&(l=ri(l,"reportingSessionId",a.Y));a.A>0&&(l=ri(l,"retryCount",a.A));a.C!=null&&(l=ri(l,"previousErrorSendStatus",a.C));a.P&&(l=ri(l,"errorSenderType",a.Ra()),b.errorSender_frontIndex&&(l=ri(l,"errorSender_frontIndex",b.errorSender_frontIndex)),b.errorSender_nextIndex&&(l=ri(l,"errorSender_nextIndex",b.errorSender_nextIndex)),b.errorSender_queueSize&&(l=ri(l,"errorSender_queueSize",b.errorSender_queueSize)));a.g.send(l,b.m,b.c,b.h);return a.J}catch(m){b=m;if(b==null)b=new ce,de(b),M(b,
Error(b));else if(qe(b))b=O(b,qe,ce);else if(xh(b))b=O(b,xh,wh),b=ke(b);else throw Fe("Unsupported type cannot be used to create a Throwable.").g;if(b instanceof Jj)a.U=!0;else throw ai(m,{"docs-origin-class":"docs.debug.ErrorSender"});}return yj()}q.wb=function(){var a=qm(this.g),b=this.J,c=rm(this.g)||a>=400&&a<=500,d=this.A>3;c||d?(this.A=0,this.C=null,this.j.stop(),rj(pm(this),function(){mj(b);nj(b,!0)})):(this.A++,this.C=a===-1?this.g.C:a,mm(this),mj(b),nj(b,!0))};
function mm(a){a.A!=1||a.j.isActive()?a.j.start():hm(a.j,!0,!0)}q.M=function(){ti(this.H,this.j,this.g,this.L);this.N.clear();X.prototype.M.call(this)};q.Ra=ba("BaseErrorSender");function sm(a,b,c,d,e){lm.call(this,a,b,c,void 0,d,e,void 0);this.o=[]}u(sm,lm);sm.prototype.enqueue=function(a){this.o.push(a);return yj()};function pm(a){a.o.shift();return yj()}sm.prototype.Ra=ba("MemoryErrorSender");sm.prototype.M=function(){delete this.o;lm.prototype.M.call(this)};function tm(a){this.g=ud(Yd(),Lc(a));a=md(this.g,1);this.j=Math.floor(Math.random()*100)<a}tm.prototype.toString=function(){var a="{bool="+!(this.j?!ld(this.g,5):!ld(this.g,2))+', string="',b=this.j?qd(this.g,6):od(this.g,3);a=a+(b!=null?String(b):"")+'", int=';b=this.j?uc(E(this.g,7,void 0,Vc)):md(this.g,4,-1);return a+(b!=null?Number(b):-1)+"}"};function um(a){this.g=new Map;this.j=[];if(a=a.get("docs-cei")){var b=a.i;b&&eb(this.j,b);a=a.cf||{};for(var c in a)this.g.set(c,new tm(a[c]))}}um.prototype.get=function(a){return this.g.get(a)||null};function vm(){for(var a in Array.prototype)return!1;return!0};function wm(a){this.g=a}function xm(a){var b=a.g;if(b==null)return null;if(typeof b==="string")return b;throw new TypeError("Invalid string data <K1cgmc>: "+a.g+" (typeof "+typeof a.g+")");}wm.prototype.toString=function(){var a=xm(this);if(a===null)throw Error("Data K1cgmc not defined.");return a};function ym(a){this.D=D(a)}u(ym,G);function zm(a){this.D=D(a)}u(zm,G);var Am=[4,5];function Bm(a){this.D=D(a)}u(Bm,G);function Cm(){var a=y;a=a===void 0?window:a;var b=new wm(Sd("K1cgmc",a));a=new Bm;b=xm(b);a=b===null?a:td(Bm,"["+b.substring(4));b=a.D;var c=b[B]|0;this.g=Lb(a,c)?a:Pc(a,b,c)?Qc(a,b):new a.constructor(Oc(b,c,!0))}
Cm.prototype.la=function(){var a=new Map,b;(b=this.g)==null?b=void 0:(b=id(b,zm,1),b=id(b,ym,fd(b,Am,4)));if(b==null?0:tc(E(b,2))!=null){var c,d=(c=pd(b,2))==null?void 0:c.toString();d&&a.set("canaryanalysisservertestgroup",d);if(b==null)var e=void 0;else if((c=F(b,Vd,3))==null)e=void 0;else{b=Number;e=e===void 0?"0":e;d=E(c,1);var f=!0;f=f===void 0?!1:f;var g=typeof d;d!=null&&(g==="bigint"?d=String(kc(64,d)):sc(d)?g==="string"?(sc(d),f=nc(Number(d)),lc(f)?d=String(f):(f=d.indexOf("."),f!==-1&&(d=
d.substring(0,f)),d=wc(d))):d=f?yc(d):xc(d):d=void 0);e=b(d!=null?d:e);c=md(c,2);e=(new Date(e*1E3+c/1E6)).valueOf().toString()}e&&a.set("serverstarttimemillis",e)}var h,l;(e=(h=this.g)==null?void 0:(l=F(h,zm,1))==null?void 0:pd(l,6))&&a.set("clientApp",String(e));return a};function Dm(){}Dm.prototype.la=function(){var a=new Map;Em()&&a.set("apps_telemetry.screen_tampered","true");a:{var b=w(Array.prototype);for(b=b.next();!b.done;b=b.next()){b=!0;break a}b=!1}b&&a.set("apps_telemetry.array_prototype_tampered","true");return a};function Em(){if("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)return!1;var a=y.screen,b=!(a instanceof Screen);try{var c=k();a.addEventListener("change",c);a.removeEventListener("change",c)}catch(d){b=!0}return b};function Fm(){}Fm.prototype.la=function(){if("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)return new Map;try{var a=Array.from(document.querySelectorAll("script")).filter(this.j).slice(0,30).map(this.g).join("\n")}catch(b){a="Error getting cross-origin scripts"}return(new Map).set("apps_telemetry.cross_origin_scripts",a)};
Fm.prototype.j=function(a){var b=new RegExp(/^(?:https?:\/\/)?(?:[a-zA-Z0-9-]+\.)*google\.com(?:$|[\/#?])/);return(a=a.getAttribute("src"))?!(a.startsWith("/")||b.test(a)):!1};Fm.prototype.g=function(a){return a.innerHTML?a.outerHTML.slice(0,a.outerHTML.indexOf(a.innerHTML)):a.outerHTML};function Gm(a){return a instanceof Error||a&&a.message!==void 0?a.message:Hm(a)}function Im(a){return a instanceof Error||a&&a.stack!==void 0?a.stack||"":""}function Jm(a,b){var c=a&&a.cause!==void 0;if(b>=3||!c)return null;c=Ck(new Ak,"");a=a.cause;if(a instanceof Error||a.message!==void 0&&a.stack!==void 0){Bk(c,Gm(a));var d=Im(a);lf(d);c.g=d;if(b=Jm(a,b+1))c.j=b}else Bk(c,Hm(a));return Dk(c)}
function Hm(a){try{return a&&a instanceof Object?JSON.stringify(a):String(a)}catch(b){return String(a)}};/*

Math.uuid.js (v1.4)
http://www.broofa.com
mailto:<EMAIL>
Copyright (c) 2010 Robert Kieffer
Dual licensed under the MIT and GPL licenses.
*/
var Km="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function Lm(a){var b=void 0;b=b===void 0?[]:b;try{var c=Td=Td||new Ud;var d=Wd.key in c.g?Wd.ctor(c.g[Wd.key]):Wd.defaultValue,e=void 0===Nb?2:4;c=void 0;var f=d.D,g=f[B]|0,h=Lb(d,g)?1:e;c=!!c||h===3;h===2&&Sc(d)&&(f=d.D,g=f[B]|0);var l=cd(f,1),m=l===Eb?7:l[B]|0,n=dd(m,g);if(d=4&n?!1:!0){4&n&&(l=Array.prototype.slice.call(l),m=0,n=bd(n,g),g=Zc(f,g,1,l));for(var t=e=0;e<l.length;e++){var x=zc(l[e]);x!=null&&(l[t++]=x)}t<e&&(l.length=t);x=n|=4;x&=-513;n=x&-1025;n&=-4097}n!==m&&(Gb(l,n),2&n&&Object.freeze(l));
var v=l=$c(l,n,f,g,1,h,d,c)}catch(N){v=[]}a=a===void 0?[]:a;f=b;g=v;b=[Error("uncaught error").message];Rk();v=Lf();if(g.length!=0){h=v.add;l=Lf();for(m=0;m<g.length;m=m+1|0)l.add(W(O(g[m],ug,df)));h.call(v,Tk(l,l,7))}v.za(Hk);v.add(Ik);v.add(Jk);v.add(Kk);v.add(Lk);v.add(Mk);v.add(Nk);v.add(Ok);for(g=0;g<f.length;g=g+1|0)v.add(O(f[g],uk,tk));if(b.length!=0){f=Lf();for(g=0;g<b.length;g=g+1|0)f.add(O(b[g],ug,df));b=v.add;g=new Xk;h=Lf();g.j=3;g.g=5;g.l=f;g.A=h;g.o=!0;b.call(v,g)}b=new Gk;b.g=!1;b.j=
v;this.l=b;this.g=[new Cm,new Dm,new Fm];this.g.push.apply(this.g,oa(a));a=[];a[8]=a[13]=a[18]=a[23]="-";a[14]="4";for(v=0;v<36;v++)a[v]||(b=0|Math.random()*16,a[v]=Km[v==19?b&3|8:b]);this.j=a.join("")}function Mm(a,b,c,d){d["apps_telemetry.session_id"]=a.j;"apps_telemetry.processed"in d&&(d["apps_telemetry.multi_processed"]="true");var e=a.la();(a=Nm(a,b,c,e))&&Om(e,a.g);e.forEach(function(g,h){d[h]=g});var f;return(f=a==null?void 0:a.j)!=null?f:c}
function Nm(a,b,c,d){try{var e=Bk(Ck(new Ak,""),Gm(b)),f=Im(b);lf(f);e.g=f;var g=Jm(b,0);g&&(e.j=g);c&&Ck(e,c);var h=Dk(e);var l=a.l,m=h.o,n=ag();try{l.g&&n.W("apps_telemetry.after_downgraded_severe","true");for(a=0;a<l.j.size();a=a+1|0){var t=O(l.j.Z(a),uk,tk);if(t.B(h)){var x=t.j,v=t.g,N=new zk;lf(x);N.j=x;lf(v);N.g=v;var Q=N}else Q=null;var R=Q;if(R!=null){h=m;t=void 0;Q=l;x=m;v=Pk;var Wa=v.contains,Xa=(oh(),nh);t=J(Xa,mh)?x.toLocaleUpperCase():x.toUpperCase();if(Wa.call(v,t)){Q.g=!0;var ub="WARNING"}else ub=
x;Wa=h;Xa=ub;var Wb=ag();Wb.W("apps_telemetry.classification",""+R.j);Wb.W("apps_telemetry.classification_code",R.g!=null?""+R.g:"");Wb.W("apps_telemetry.incoming_severity",Wa);Wb.W("apps_telemetry.outgoing_severity",Xa);R=n;S(Wb);for(var Ql=Wb.aa().F();Ql.g();){var Rl=O(Ql.j(),U,V);R.W(Rl.O(),Rl.R())}m=ub;break}}n.W("apps_telemetry.processed","true")}catch(Bh){var Ch=ke(Bh);if(Ch instanceof P)n.W("apps_telemetry.processed","false"),n.W("apps_telemetry.handling_error",Qk(l,Ch,0));else throw Ch.g;
}l=m;var Wc=new Uk;lf(l);Wc.j=l;lf(n);Wc.g=n;return Wc}catch(Bh){Pm(d,Bh,"apps_telemetry.processed")}return null}Lm.prototype.la=function(){var a=new Map;try{for(var b=w(this.g),c=b.next();!c.done;c=b.next())c.value.la().forEach(function(d,e){a.set(e,d)})}catch(d){Pm(a,d,"apps_telemetry.annotated")}return a};function Om(a,b){b.Ua().Xa().forEach(function(c){a.set(c,b.get(c))})}function Pm(a,b,c){a.set(c,"false");a.set("apps_telemetry.handling_error",Hm(b))};y.U3bHHf!=null||(y.U3bHHf=0);y.U3bHHf++;function Qm(a,b){var c=a.__wiz;c||(c=a.__wiz={});return c[b.toString()]};/*

 Copyright 2024 Google, Inc
 SPDX-License-Identifier: MIT
*/
var Rm={};var Sm={};function Tm(a){var b=document.body,c=Na(b.getAttribute("jsaction")||"");var d=["u0pjoe"];for(var e=w(d),f=e.next();!f.done;f=e.next()){f=f.value;var g;if(g=c){var h=Rm[g];h?g=!!h[f.toString()]:(h=Sm[f.toString()],h||(h=new RegExp("(^\\s*"+f+"\\s*:|[\\s;]"+f+"\\s*:)"),Sm[f.toString()]=h),g=h.test(g))}else g=!1;g||(c&&!/;$/.test(c)&&(c+=";"),c+=f+":.CLIENT",Um(b,c));(g=Qm(b,f))?g.push(a):b.__wiz[f.toString()]=[a]}return{et:d,lb:a,el:b}}
function Um(a,b){a.setAttribute("jsaction",b);"__jsaction"in a&&delete a.__jsaction};function Vm(a){X.call(this);this.j=a}z(Vm,X);Vm.prototype.g=function(a){return Wm(this,a)};function Xm(a,b){a=Object.prototype.hasOwnProperty.call(a,Da)&&a[Da]||(a[Da]=++Ea);return(b?"__wrapper_":"__protected_")+a+"__"}function Wm(a,b){var c=Xm(a,!0);b[c]||((b[c]=Ym(a,b))[Xm(a,!1)]=b);return b[c]}function Ym(a,b){function c(){if(a.wa())return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){Zm(a,d)}}c[Xm(a,!1)]=b;return c}
function Zm(a,b){if(!(b&&typeof b==="object"&&typeof b.message==="string"&&b.message.indexOf("Error in protected function: ")==0||typeof b==="string"&&b.indexOf("Error in protected function: ")==0))throw a.j(b),new $m(b);}function an(a){var b=b||y.window||y.globalThis;"onunhandledrejection"in b&&(b.onunhandledrejection=function(c){Zm(a,c&&c.reason?c.reason:Error("uncaught error"))})}
function bn(a,b){var c=y.window||y.globalThis,d=c[b];if(!d)throw Error(b+" not on global?");c[b]=function(e,f){typeof e==="string"&&(e=Ia(Ja,e));e&&(arguments[0]=e=Wm(a,e));if(d.apply)return d.apply(this,arguments);var g=e;if(arguments.length>2){var h=Array.prototype.slice.call(arguments,2);g=function(){e.apply(this,h)}}return d(g,f)};c[b][Xm(a,!1)]=d}
Vm.prototype.M=function(){var a=y.window||y.globalThis;var b=a.setTimeout;b=b[Xm(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[Xm(this,!1)]||b;a.setInterval=b;Vm.ba.M.call(this)};function $m(a){La.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&typeof a==="string"&&(this.stack=a)}z($m,La);function cn(){};var dn;function en(){}z(en,cn);en.prototype.j=function(){return new XMLHttpRequest};dn=new en;function fn(a){bm.call(this);this.headers=new Map;this.Y=a||null;this.l=!1;this.g=null;this.N="";this.C=0;this.o=this.L=this.H=this.J=!1;this.P=0;this.A=null;this.T="";this.X=!1}z(fn,bm);var gn=/^https?$/i,hn=["POST","PUT"],jn=[];q=fn.prototype;q.mb=function(){this.dispose();db(jn,this)};
q.send=function(a,b,c,d){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.N+"; newUri="+a);b=b?b.toUpperCase():"GET";this.N=a;this.C=0;this.J=!1;this.l=!0;this.g=this.Y?this.Y.j():dn.j();this.g.onreadystatechange=vi(Ha(this.Wa,this));try{this.L=!0,this.g.open(b,String(a),!0),this.L=!1}catch(g){kn(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get===
"function"){e=w(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=y.FormData&&a instanceof y.FormData;!(bb(hn,b)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=w(c);for(d=b.next();!d.done;d=b.next())c=w(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,c);this.T&&(this.g.responseType=
this.T);"withCredentials"in this.g&&this.g.withCredentials!==this.X&&(this.g.withCredentials=this.X);try{this.A&&(clearTimeout(this.A),this.A=null),this.P>0&&(this.A=setTimeout(this.Fb.bind(this),this.P)),this.H=!0,this.g.send(a),this.H=!1}catch(g){kn(this)}};q.Fb=function(){typeof xa!="undefined"&&this.g&&(this.C=8,this.dispatchEvent("timeout"),this.abort(8))};function kn(a){a.l=!1;a.g&&(a.o=!0,a.g.abort(),a.o=!1);a.C=5;ln(a);mn(a)}
function ln(a){a.J||(a.J=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))}q.abort=function(a){this.g&&this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1,this.C=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),mn(this))};q.M=function(){this.g&&(this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1),mn(this,!0));fn.ba.M.call(this)};q.Wa=function(){this.wa()||(this.L||this.H||this.o?nn(this):this.Ha())};q.Ha=function(){nn(this)};
function nn(a){if(a.l&&typeof xa!="undefined")if(a.H&&(a.g?a.g.readyState:0)==4)setTimeout(a.Wa.bind(a),0);else if(a.dispatchEvent("readystatechange"),(a.g?a.g.readyState:0)==4){a.l=!1;try{rm(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.C=6,ln(a))}finally{mn(a)}}}function mn(a,b){if(a.g){a.A&&(clearTimeout(a.A),a.A=null);var c=a.g;a.g=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}}q.isActive=function(){return!!this.g};
function rm(a){var b=qm(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.N).match(li)[1]||null,!a&&y.self&&y.self.location&&(a=y.self.location.protocol.slice(0,-1)),b=!gn.test(a?a.toLowerCase():"");c=b}return c}function qm(a){try{return(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(b){return-1}}Bi(function(a){fn.prototype.Ha=a(fn.prototype.Ha)});function on(a,b,c){bm.call(this);this.A=b||null;this.o={};this.C=pn;this.J=a;if(!c){this.g=null;this.g=new Vm(Ha(this.l,this));bn(this.g,"setTimeout");bn(this.g,"setInterval");a=this.g;b=y.window||y.globalThis;c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame","msRequestAnimationFrame"];for(var d=0;d<c.length;d++){var e=c[d];c[d]in b&&bn(a,e)}a=this.g;Ai=!0;b=Ha(a.g,a);for(c=0;c<yi.length;c++)yi[c](b);zi.push(a)}}z(on,bm);
function qn(a,b){xl.call(this,"c");this.error=a;this.ea=b}z(qn,xl);function rn(a,b){return new on(a,b,void 0)}function pn(a,b,c,d){if(d instanceof Map){var e={};d=w(d);for(var f=d.next();!f.done;f=d.next()){var g=w(f.value);f=g.next().value;g=g.next().value;e[f]=g}}else e=d;d=new fn;jn.push(d);d.j.add("ready",d.mb,!0,void 0,void 0);d.send(a,b,c,e)}function sn(a,b){a.C=b}
on.prototype.l=function(a,b){a=a.error||a;b=b?$k(b):{};a instanceof Error&&bl(b,pb(a));var c=Vh(a);if(this.A)try{this.A(c,b)}catch(x){}var d=c.message.substring(0,1900);if(!(a instanceof La)||a.g){var e=c.fileName,f=c.lineNumber;a=c.stack;try{var g=ri(this.J,"script",e,"error",d,"line",f);a:{for(var h in this.o){var l=!1;break a}l=!0}if(!l){l=g;var m=qi(this.o);g=ni(l,m)}m={};m.trace=a;if(b)for(var n in b)m["context."+n]=b[n];var t=qi(m);this.C(g,"POST",t,this.H)}catch(x){}}try{this.dispatchEvent(new qn(c,
b))}catch(x){}};on.prototype.M=function(){si(this.g);on.ba.M.call(this)};function tn(){this.g=Date.now()}var un=null;tn.prototype.set=function(a){this.g=a};tn.prototype.reset=function(){this.set(Date.now())};tn.prototype.get=p("g");function vn(a){this.o=a||"";un||(un=new tn);this.B=un}vn.prototype.g=!0;vn.prototype.j=!0;vn.prototype.l=!1;function wn(a){return a<10?"0"+a:String(a)}function xn(a){vn.call(this,a)}z(xn,vn);
function yn(a,b){var c=[];c.push(a.o," ");if(a.j){var d=c.push,e=new Date(b.l());d.call(c,"[",wn(e.getFullYear()-2E3)+wn(e.getMonth()+1)+wn(e.getDate())+" "+wn(e.getHours())+":"+wn(e.getMinutes())+":"+wn(e.getSeconds())+"."+wn(Math.floor(e.getMilliseconds()/10)),"] ")}d=c.push;e=a.B.get();e=(b.l()-e)/1E3;var f=e.toFixed(3),g=0;if(e<1)g=2;else for(;e<100;)g++,e*=10;for(;g-- >0;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.j(),"] ");c.push(b.getMessage());a.l&&(b=b.g(),b!==void 0&&c.push("\n",b instanceof
Error?b.message:String(b)));a.g&&c.push("\n");return c.join("")};function zn(a){a=a===void 0?new An:a;bm.call(this);var b=this;this.T={};this.g=null;this.l={};this.N=new jm(this);this.pb=a.A;this.X=a.L;this.eb=a.H;this.nb=a.B;this.fb=a.N;var c=a.j;this.bb=new Lm(a.J);this.kb=a.U;this.Y=new wl;var d=new fn;Bn(this,c);this.H=new sm(d,c,void 0,void 0,void 0);ui(this,this.H);this.o=a.g?a.g:Ij(c,"docs-sup")+Ij(c,"docs-jepp")+"/jserror";if(d=Ij(c,"jobset"))this.o=ri(this.o,"jobset",d);if(d=Ij(c,"docs-ci"))this.o=ri(this.o,"id",d);d=Ij(c,"docs-pid");Fj(c.get("docs-eaotx"))&&
d&&(this.o=ri(this.o,"ouid",d));this.Oa=Hj(c,"docs-srmoe")||0;this.ib=Fj(c.get("docs-oesf"));this.Pa=Hj(c,"docs-srmour")||0;this.jb=Fj(c.get("docs-oursf"));d=a.o||this.Pa>0&&Math.random()<this.Pa;this.gb=Fj(c.get("docs-wesf"));this.Qa=Hj(c,"docs-srmwe")||0;Cn(this);jj=function(g){return Dn(b,g,"promise rejection")};var e=Hj(c,"docs-srmdue")||0;if(e>0&&Math.random()<e){var f=Fj(c.get("docs-duesf"));qj=function(g){Dn(b,g,"deferred error",f,"isDeferredUnhandledErrback")}}else qj=k();Hj(c,"docs-srmxue");
c.get("docs-xduesf");d&&(d=new Vm(function(g){var h={};h=(h.isUnhandledRejection="true",h);b.jb?En(b,g,h):b.info(g,h)}),an(d),ui(this,d));this.L=null;this.Qa>0&&Math.random()<this.Qa&&document&&document.body&&(this.L=Tm(function(g){var h={};h=(h.isWizError="true",h);g=w(g.data.errors);for(var l=g.next();!l.done;l=g.next())l=l.value.error,b.gb?En(b,l,h):b.info(l,h)}));this.P=a.l;this.C=!1;this.J=!0;this.A=!1;this.qa=Ij(c,"docs-jern");this.cb=a.C;this.ab=a.G.concat(Object.values(Nh))}u(zn,bm);
function Cn(a){var b=b===void 0?!1:b;if(Fn){if(Gn!=null)throw Error('ErrorReporter already installed. at "'+Gn.stack+'"');throw Error("ErrorReporter already installed.");}Fn=!0;Gn=Error();a.g=rn(a.o,function(e,f){return Hn(a,e,f)});var c={};a.eb&&(c["X-No-Abort"]="1");a.g.H=c;sn(a.g,function(e,f,g,h){a.J&&a.H.send(e,f,g,h)});if(a.Oa>0&&Math.random()<a.Oa){c={};var d=(c.isWindowOnError="true",c);a.ib?Uh(function(e){En(a,e.error instanceof Error?e.error:Error(e.message),d)}):Uh(function(e){a.log(e.error instanceof
Error?e.error:Error(e.message),d)})}a.N.listen(a.g,"c",function(e){e.ea.severity=e.ea["severity-unprefixed"]||e.ea.severity;var f=e.ea.severity;(f=f=="fatal"||f=="postmortem")&&!a.nb&&(!a.pb||(b===void 0?0:b)?a.Y.notify(void 0,e.ea):a.Y.notify(e,e.ea));a.dispatchEvent(new Xl(f?"a":"b",e.error,e.ea))})}function Bn(a,b){b=new um(b);var c=b.g,d;for(d in c){var e=c[d];e&&(a.l["expflag-"+d]=e.toString())}a.l.experimentIds=b.j.join(",")}
function En(a,b,c){a.A=!1;In(b,"fatal");if(!a.g){if(b instanceof vh)throw b.g;throw ai(b);}a.g.l(b,Jn(a,b,c));if(a.fb){c=Jn(a,b,c);c.is_forceFatal=1;var d=b instanceof vh?b.g:b;Hn(a,d,c);b=ai(d);a=", context:"+JSON.stringify(Jn(a,d,c));b.message+=a;throw b;}}function Kn(a,b,c){a.A=!1;In(b,"warning");a.g&&a.g.l(b,Jn(a,b,c))}zn.prototype.info=function(a,b,c){this.A=c||!1;In(a,"incident");this.g&&this.g.l(a,Jn(this,a,b))};
zn.prototype.log=function(a,b,c){this.A=!!c;In(a,"incident");this.g&&this.g.l(a,Jn(this,a,b))};
function Dn(a,b,c,d,e){d=d===void 0?!0:d;if(b&&typeof b==="object"&&b.type==="error"){var f=b.error;b=JSON.stringify({error:f&&f.message?f.message:"Missing error cause.",stack:f&&f.stack?f.stack:"Missing error cause.",message:b.message,filename:b.filename,lineno:b.lineno,colno:b.colno,type:b.type});c=Error("Unhandled "+c+" with ErrorEvent: "+b)}else c=typeof b==="string"?Error("Unhandled "+c+" with: "+b):b==null?Error("Unhandled "+c+' with "null/undefined"'):b;b={};e&&(b[e]="true");d?Ma(c):a.info(c,
b)}function Ln(a,b,c){return function(){a:{var d=ra.apply(0,arguments);if(a.g){try{var e=b.apply(c,d);break a}catch(f){En(a,f)}e=void 0}else e=b.apply(c,d)}return e}}function Mn(a,b){a.g&&b.then(void 0,function(c){En(a,c instanceof Error?c:Error(c))});return b}function Jn(a,b,c){b instanceof vh&&(b=b.g);c=c?$k(c):{};c.severity=pb(b).severity;a.X&&(c.errorGroupId=a.X);return c}
function Hn(a,b,c){var d=a.C;try{a.ga(b,c)}catch(f){throw d&&!a.P&&(a.J=!1),a.C=!0,c.provideLogDataError=f.message,c.severity||(c.severity="fatal"),ai(f);}finally{if(c["severity-unprefixed"]=c.severity||"fatal",c.severity=""+c["severity-unprefixed"],!a.cb)for(var e in c)typeof c[e]==="number"||c[e]instanceof Number||typeof c[e]==="boolean"||c[e]instanceof Boolean||a.ab.includes(e)||e in c&&delete c[e]}}
zn.prototype.ga=function(a,b){for(var c in this.T)try{b[c]=this.T[c](a)}catch(g){}bl(b,this.l);if((ki(),0)>0){var d=new xn,e="";ji(function(g){e+=yn(d,g)});b.clientLog=e}c=b.severity||"fatal";this.kb||(c=Mm(this.bb,a,c,b));this.qa&&(b.reportName=this.qa+"_"+c);b.isArrayPrototypeIntact=vm().toString();if(!("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)){try{var f=!!document.getElementById("docs-editor")}catch(g){f=!1}b.isEditorElementAttached=f.toString()}b.documentCharacterSet=document.characterSet;
f=a.stack||"";if(f.trim().length==0||f=="Not available")b["stacklessError-reportingStack"]=$h(zn.prototype.ga),[a.message].concat(oa(Object.keys(b)),oa(Object.values(b))).some(function(g){return g&&g.includes("<eye3")})||(b.eye3Hint="<eye3-stackless title='Stackless JS Error - "+a.name+"'/>");this.C&&!this.P?(this.J=this.A,c=="fatal"?c="postmortem":c=="incident"&&(c="warningafterdeath")):c=="fatal"&&(this.C=!0);this.A=!1;b.severity=c};
zn.prototype.M=function(){Fn=!1;if(this.L)for(var a=this.L,b=w(a.et),c=b.next();!c.done;c=b.next()){c=c.value;var d=Qm(a.el,c);if(d&&(db(d,a.lb),!d.length)){d=a.el;var e=Na(d.getAttribute("jsaction")||"");c+=":.CLIENT";e=e.replace(c+";","");e=e.replace(c,"");Um(d,e)}}ti(this.N,this.g,this.H);bm.prototype.M.call(this)};var Fn=!1,Gn=null;function An(){this.L=this.j=void 0;this.B=this.N=this.A=!1;this.g=void 0;this.H=this.l=!1;this.C=!0;this.G=[];this.U=this.o=!1;this.J=[]}
function In(a,b){a instanceof vh&&(a=a.g);ob(a,"severity",b)};function Nn(a){this.o=a.Ib||null;this.l=a.Ec||!1;this.g=void 0}z(Nn,cn);Nn.prototype.j=function(){var a=new On(this.o,this.l);this.g&&(a.H=this.g);return a};function On(a,b){bm.call(this);this.ga=a;this.L=b;this.H=void 0;this.status=this.readyState=0;this.responseType=this.o=this.l=this.statusText="";this.onreadystatechange=null;this.P=new Headers;this.A=null;this.X="GET";this.Y="";this.g=!1;this.T=this.C=this.J=null;this.N=new AbortController}z(On,bm);q=On.prototype;
q.open=function(a,b){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.X=a;this.Y=b;this.readyState=1;Pn(this)};q.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");if(this.N.signal.aborted)throw this.abort(),Error("Request was aborted.");this.g=!0;var b={headers:this.P,method:this.X,credentials:this.H,cache:void 0,signal:this.N.signal};a&&(b.body=a);(this.ga||y).fetch(new Request(this.Y,b)).then(this.ub.bind(this),this.ua.bind(this))};
q.abort=function(){this.l=this.o="";this.P=new Headers;this.status=0;this.N.abort();this.C&&this.C.cancel("Request was aborted.").catch(k());this.readyState>=1&&this.g&&this.readyState!=4&&(this.g=!1,Qn(this));this.readyState=0};
q.ub=function(a){if(this.g&&(this.J=a,this.A||(this.status=this.J.status,this.statusText=this.J.statusText,this.A=a.headers,this.readyState=2,Pn(this)),this.g&&(this.readyState=3,Pn(this),this.g)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.sb.bind(this),this.ua.bind(this));else if(typeof y.ReadableStream!=="undefined"&&"body"in a){this.C=a.body.getReader();if(this.L){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.l=
[]}else this.l=this.o="",this.T=new TextDecoder;Rn(this)}else a.text().then(this.tb.bind(this),this.ua.bind(this))};function Rn(a){a.C.read().then(a.rb.bind(a)).catch(a.ua.bind(a))}q.rb=function(a){if(this.g){if(this.L&&a.value)this.l.push(a.value);else if(!this.L){var b=a.value?a.value:new Uint8Array(0);if(b=this.T.decode(b,{stream:!a.done}))this.l=this.o+=b}a.done?Qn(this):Pn(this);this.readyState==3&&Rn(this)}};q.tb=function(a){this.g&&(this.l=this.o=a,Qn(this))};
q.sb=function(a){this.g&&(this.l=a,Qn(this))};q.ua=function(){this.g&&Qn(this)};function Qn(a){a.readyState=4;a.J=null;a.C=null;a.T=null;Pn(a)}q.setRequestHeader=function(a,b){this.P.append(a,b)};q.getResponseHeader=function(a){return this.A?this.A.get(a.toLowerCase())||"":""};q.getAllResponseHeaders=function(){if(!this.A)return"";for(var a=[],b=this.A.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
function Pn(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(On.prototype,"withCredentials",{get:function(){return this.H==="include"},set:function(a){this.H=a?"include":"same-origin"}});function Sn(a){this.g=null;this.j=a<1;this.l=a<.01}function Tn(a,b,c){c=c===void 0?{}:c;a.l&&(c.sampling_samplePercentage=(.01).toString(),a.g.info(b,c))}function Un(a,b,c){c=c===void 0?{}:c;a.j&&(c.sampling_samplePercentage=(1).toString(),Kn(a.g,b,c))};function Vn(a){this.D=D(a)}u(Vn,G);Vn.prototype.getMessage=function(){return od(this,1)};function Wn(a){this.D=D(a)}u(Wn,G);function Xn(a){this.D=D(a)}u(Xn,G);function Yn(a){this.D=D(a)}u(Yn,G);function Zn(a,b){return sd(a,b)}Yn.prototype.Ta=function(){return F(this,Wn,3)};Yn.prototype.Ca=function(){return F(this,Vn,5)};function $n(a){this.D=D(a)}u($n,G);function ao(a){var b=new $n;return sd(b,a)}$n.prototype.Ca=function(){return F(this,Vn,3)};$n.prototype.Ta=function(){return F(this,Wn,4)};function bo(a){var b=Yi();chrome.runtime.sendMessage(Kc(a),void 0,function(c){return co(b,function(d){return new $n(d)},c)});return b.promise.catch(function(c){c=Zh(c);ob(c,"offscreenDocumentRequestType",pd(a,1).toString());throw c;})}
function co(a,b,c){var d=chrome.runtime;c!==void 0?(d=b(c),d.Ca()?(b=a.reject,c=Error,d=d.Ca(),d=qd(d,1),b.call(a,c("Error from Offscreen page:"+d))):a.resolve(d)):a.reject(Error("No response from Offscreen page:"+(d.lastError?d.lastError.message:"without lastError")))};function eo(){return fo(chrome.storage.local,["optedInUserOuid"]).then(function(a){return a.optedInUserOuid||null})}function go(a){return ho({offlineOptedIn:!0}).then(function(){if(a){var b={};return ho((b.optedInUserOuid=a,b))}})}function io(){return ho({offlineOptedIn:!1}).then(function(){return jo()})}
function ko(){return fo(chrome.storage.local,["offlineOptedIn"]).then(function(a){a=a.offlineOptedIn;switch(a){case void 0:return"unknown";case !0:return"opted_in";case !1:return"opted_out";default:throw Error("Cannot handle opt in value "+a);}})}function Wi(){return fo(chrome.storage.managed,["allowedDocsOfflineDomains"]).then(function(a){return a&&a.allowedDocsOfflineDomains?a.allowedDocsOfflineDomains:[]})}
function Xi(){return fo(chrome.storage.managed,["autoEnabledDocsOfflineDomains"]).then(function(a){return a&&a.autoEnabledDocsOfflineDomains?a.autoEnabledDocsOfflineDomains:[]})}function fo(a,b){return new Y(function(c,d){a.get(b,function(e){chrome.runtime.lastError?d(Error(chrome.runtime.lastError)):c(e)})})}function ho(a){return new Y(function(b,c){chrome.storage.local.set(a,function(){chrome.runtime.lastError?c(Error(chrome.runtime.lastError)):b()})})}
function jo(){return new Y(function(a,b){chrome.storage.local.remove("optedInUserOuid",function(){chrome.runtime.lastError?b(Error(chrome.runtime.lastError)):a()})})}function lo(){return fo(chrome.storage.local,["lastSuccessfulFrameConnectTime"]).then(function(a){return a.lastSuccessfulFrameConnectTime||null})};function mo(a){this.D=D(a)}u(mo,G);function no(a){this.D=D(a)}u(no,G);function oo(a){this.D=D(a)}u(oo,G);function po(a){this.D=D(a)}u(po,G);function qo(a){this.D=D(a)}u(qo,G);function ro(a){this.D=D(a)}u(ro,G);function so(a){var b=new ro;return sd(b,a)}function to(a,b){return kd(a,po,5,b)};function uo(a,b,c){X.call(this);this.A=null;this.J=a;this.C=b;this.o=c;this.j=Yi();this.l=!1;a=new cl;fl(a,"offscreendocument.html");ql(a,"randomPercentageForSampling",this.o);ql(a,"sessionId",this.C);this.H={url:a.toString(),reasons:["IFRAME_SCRIPTING"],justification:"Use iframe to access user data under docs.google.com domain"};this.g=new Sn(this.o)}u(uo,X);function vo(a,b){a.A=b}
function wo(a){return xo().then(function(b){return b?bo(so(5)).then(function(){return chrome.offscreen.closeDocument()}):Promise.resolve()}).then(function(){yo(a)})}function zo(a,b){return Ao(Bo(a,Co(a,6,b)))}function Do(a,b){b=Co(a,1,b);return Ao(Bo(a,b))}function Eo(a,b){return xo().then(function(c){b[Fo(0)]=c.toString();return c?Promise.resolve():Go(a)})}
function Go(a){return chrome.offscreen.createDocument(a.H).catch(function(b){if(b instanceof Error&&b.message.includes("Only a single offscreen document may be created"))Tn(a.g,b);else return Promise.reject(b)})}function Ho(a,b){return chrome.offscreen.closeDocument().catch(function(c){c=c instanceof Error?c.message:c.toString();Tn(a.g,Error(c),b);b.errorWhenForceCloseOffscreenDoc=c}).then(function(){return Go(a)})}
function Co(a,b,c){b=so(b);var d=new mo;c=rd(d,1,c);c=rd(c,2,a.A.toString());a=rd(c,3,a.J);a=rd(a,4,"opted_in");return kd(b,mo,2,a)}
function Io(a,b){var c={sendingFrameRequestType:pd(b,1)},d=to(so(4),b);return Ao(Jo(a,d,c,0)).catch(function(e){if(e instanceof Error){c.offlineFrameConnected_afterFirstError=a.l;if(Ko(e.message))return Tn(a.g,e,c),new Promise(function(f){return setTimeout(function(){return f(Jo(a,d,c,1))},2E3)});if("Requests cancelled because user has been opted out"==e.message)return Promise.resolve(new Wn)}return Promise.reject(e instanceof Error?e:Error(e))})}
function Jo(a,b,c,d){return a.j.promise.then(function(){return xo()}).then(function(e){c[Fo(d)]=e.toString();return e?Ri():Lo(a)}).then(function(){return a.j.promise}).then(function(){return bo(b)}).then(function(e){return e.Ta()}).da(function(e){return Mo(e,c)})}function Lo(a){yo(a);return ko().then(function(b){return b=="opted_in"?eo().then(function(c){return Do(a,c)}):Si()})}
function Bo(a,b){pd(b,1)==6||pd(b,1);var c={sendingOffscreenDocumentRequestType:pd(b,1).toString()};return Ri(Eo(a,c)).then(function(){return em()}).then(function(){return bo(b)}).da(function(d){return d instanceof Error&&Ko(d.message)?(Tn(a.g,d,c),Ri(xo()).then(function(e){c[Fo(1)]=e.toString()}).then(function(){return Ho(a,c)}).then(function(){return em()}).then(function(){return bo(b)})):Promise.reject(d instanceof Error?d:Error(d))}).da(function(d){return Mo(d,c)})}
function Ko(a){return a.includes("Could not establish connection. Receiving end does not exist.")||a.includes("The message port closed before a response was received.")||a.includes("A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received")}function xo(){return self.clients.matchAll().then(function(a){return a.some(function(b){return b.url.includes(chrome.runtime.getURL("offscreendocument.html"))})})}
uo.prototype.M=function(){wo(this);X.prototype.M.call(this)};function Ao(a){return Promise.resolve(a)}function yo(a){a.j=Yi();a.l=!1}function Fo(a){switch(a){case 0:return"hasDocument_beforeCreatingOffscreenDoc_0";case 1:return"hasDocument_beforeCreatingOffscreenDoc_1";default:throw Error("Cannot get error context key with retryAttempt "+a);}}
function Mo(a,b){a=Zh(a);b=w(Object.entries(b));for(var c=b.next();!c.done;c=b.next()){var d=w(c.value);c=d.next().value;d=d.next().value;ob(a,c,d)}throw a;};function No(a){this.D=D(a)}u(No,G);function Oo(a){this.D=D(a)}u(Oo,G);function Po(a){this.D=D(a)}u(Po,G);function Qo(){Ha(this.l,this);this.g=new xn;this.g.j=!1;this.g.l=!1;this.j=this.g.g=!1;this.o={}}function Ro(a){1!=a.j&&(a.j=!0)}Qo.prototype.l=function(a){function b(f){if(f){if(f.value>=ei.value)return"error";if(f.value>=fi.value)return"warn";if(f.value>=gi.value)return"log"}return"debug"}if(!this.o[a.j()]){var c=yn(this.g,a),d=So;if(d){var e=b(a.o());To(d,e,c,a.g())}}};var So=y.console;function To(a,b,c,d){if(a[b])a[b](c,d===void 0?"":d);else a.log(c,d===void 0?"":d)};var Uo=new Nn({Ib:self});Uo.g="same-origin";dn=Uo;
function Vo(){X.call(this);var a=this;this.H=yh();this.g=this.A=null;this.N=!1;this.U=new Qo;Ro(this.U);this.L=Yi();chrome.alarms.onAlarm.addListener(function(b){return a.L.promise.then(function(){return Mn(a.g,Ln(a.g,a.zb,a)(b))})});chrome.runtime.onMessageExternal.addListener(function(b,c,d){return Wo(a,b,d)});chrome.runtime.onMessage.addListener(this.Ab.bind(this));this.C=new jm(this);ui(this,this.C);this.C.listen(y,"message",this.Bb);this.o=Math.random()*100;this.J=this.o<1;this.l=new Sn(this.o);
this.j=new uo(Xo(),this.H,this.o);chrome.runtime.onConnectExternal.addListener(k());dm(this.Eb,252E5,this)}u(Vo,X);q=Vo.prototype;q.load=function(){var a=this;this.A="docs.google.com";return ho({docsDomain:this.A}).then(function(){a.g=Yo(a);a.L.resolve();ui(a,a.g);a.l.g=a.g;a.j.g.g=a.g;vo(a.j,Zo(a));ui(a,a.j);var b=Ln(a.g,a.xb,a),c=Mn(a.g,Ri().then(function(){return b()}));return Ri(c)}).da(function(b){Zh(b)})};
function $o(a,b,c){return ql(ql(fl(Zo(a),"/offline/extension/report"),"v",c),"optin",b).toString()}q.Eb=function(){chrome.alarms.create("open",{delayInMinutes:1});Tn(this.l,Error("Called unsafeClose_"))};function ap(a){return new Y(function(b){chrome.alarms.get("heartbeat",function(c){c||(chrome.alarms.create("heartbeat",{periodInMinutes:5}),bp(a,"heartbeat"));b()})})}function cp(){return new Y(function(a){chrome.alarms.clear("heartbeat",function(){a()})})}
q.xb=function(){var a=this;return lo().then(function(b){a.g.l.lastSuccessfulFrameConnectTime=(b==null?void 0:b.toString())||"null"}).then(function(){return ko()}).then(function(b){var c=Xo();a.g.l.extensionVersion=c;a.g.l.optInStatus=String(b);dp(a,String(b),c);switch(b){case "unknown":break;case "opted_in":return eo().then(function(d){return Do(a.j,d)});case "opted_out":break;default:throw Error("Could not handle opt in status "+b);}})};
function dp(a,b,c){a.J&&(b=$o(a,b,c),y.fetch(new Request(b,{method:"post",mode:"cors"})).then(k()).catch(function(d){Kn(a.g,Zh(d))}))}q.Bb=function(a){var b=a.g;b&&b.data&&b.ports&&b.ports.length?(a=new Po(b.data),ep(this,a,b.ports.length>1?b.ports[1]:void 0).then(function(c){b.ports[0].postMessage(Kc(c))})):Un(this.l,Error("Dropped invalid event."),{event:String(a)})};
function Wo(a,b,c){var d=new Po(b);ep(a,d).then(function(e){c(Kc(e))}).da(function(e){if(e instanceof Error&&e.message=="Attempting to use a disconnected port object")Un(a.l,Error("Failed to reply to request because listen port was disconnected."),{requestType:tc(E(d,1,void 0,Vc))});else throw e;});return!0}
q.Ab=function(a,b,c){var d=this;a=new ro(a);switch(tc(E(a,1,void 0,Vc))){case 3:var e=F(a,no,4);a=zc(E(e,1))!=null?od(e,1):null;var f=qd(e,2);go(a).then(function(){return ho({lastSuccessfulFrameConnectTime:parseInt(f,10)})}).then(function(){var h=d.j;h.l=!0;h.j.resolve()}).then(function(){var h=ao(3);c(Kc(h))});break;case 7:var g=ao(7);(a=(e=F(a,qo,6))==null?void 0:od(e,1))?fp(this,a).then(function(){return c(Kc(g))}):gp(this).then(function(){return c(Kc(g))});break;default:throw Error("Unsupported OffscreenDocumentRequestType.");
}return!0};function ep(a,b,c){return Ri().then(a.Cb.bind(a,b,c)).da(function(d){d=d instanceof Error?d:Error(d);var e=new Yn,f=new Vn;kd(e,Vn,5,f);rd(f,1,d.message);return e})}
q.Cb=function(a){var b=this,c=Zn(new Yn,tc(E(a,1)));switch(tc(E(a,1,void 0,Vc))){case 1:return(a=(a=F(a,no,7))?qd(a,1):null)||Tn(this.l,Error("Scheduler frame connect request sent without an ouid.")),go(a).then(function(){return ho({lastSuccessfulFrameConnectTime:Date.now()})}).then(function(){var e=b.j;e.l=!0;e.j.resolve()}).then(function(){return c});case 2:var d=(a=F(a,No,8))?qd(a,1):null;return go(d).then(function(){return d?d:eo()}).then(function(e){return zo(b.j,e).then(function(){return ap(b)})}).then(function(){return c});
case 3:return(a=F(a,qo,3))&&od(a,1)?(a=od(a,1),fp(this,a).then(function(){return c})):gp(this).then(function(){return c});case 5:return hp(F(a,Oo,5)).then(function(e){kd(c,Xn,4,e);return c});case 4:return a=F(a,po,4),Ri(Io(this.j,a)).then(function(e){kd(c,Wn,3,e);return c})}throw Error("Dropped unknown message "+a);};
function hp(a){var b=qd(a,1);return Vi().then(function(c){var d=c[0],e=c[1];c=new Xn;var f=bb(d,b)>=0;d=bb(e,b)>=0;e=f||d;e=e==null?e:qc(e);Yc(c,1,e);Yc(c,2,d==null?d:qc(d));return c})}function fp(a,b){return a.N?Ri(wo(a.j)):(Tn(a.l,Error("Extension frame connected with the wrong OUID.")),a.N=!0,Ri(Do(a.j,b)))}function gp(a){return io().then(function(){return cp()}).then(function(){return wo(a.j)})}q.zb=function(a){return bp(this,a.name)};
function bp(a,b){var c=new po;c=sd(c,0);var d=new oo;b=rd(d,1,b);kd(c,oo,2,b);return Ri(Io(a.j,c))}function Yo(a){var b=fl(Zo(a),"/offline/jserror").toString(),c=a.J;a=String(a.H);var d=!0;d=d===void 0?!1:d;var e=e===void 0?Cj():e;var f=new An;f.A=!1;f.B=!0;f.g=b;f.l=d;f.j=e;f.o=!1;b=new zn(f);b.l.sessionTypeName="offline-event-page";b.l.reportsNonFatalErrors=String(c);b.l.sid=a;return b}function Zo(a){return dl(new cl("//"+a.A),"https")}
function Xo(){var a=chrome.runtime.getManifest();return a.version?a.version:"unknown"};self.window=self;(new Vo).load();
