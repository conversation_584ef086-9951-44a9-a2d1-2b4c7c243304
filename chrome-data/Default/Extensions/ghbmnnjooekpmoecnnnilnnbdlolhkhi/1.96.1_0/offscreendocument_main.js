'use strict';function aa(){return function(a){return a}}function k(){return function(){}}function p(a){return function(){return this[a]}}function ba(a){return function(){return a}}var q;function ca(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var fa=ea(this);function r(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&da(c,a,{configurable:!0,writable:!0,value:b})}}
r("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;da(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=p("g");var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});
r("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=fa[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&da(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ca(this))}})}return a});function ha(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
var ia=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
function t(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.ba=b.prototype}function v(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}
function oa(a){if(!(a instanceof Array)){a=v(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function pa(a){return qa(a,a)}function qa(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a}function ra(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}r("globalThis",function(a){return a||fa});
r("Promise",function(a){function b(g){this.g=0;this.l=void 0;this.j=[];this.H=!1;var h=this.o();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.j=function(g){if(this.g==null){this.g=[];var h=this;this.l(function(){h.v()})}this.g.push(g)};var e=fa.setTimeout;c.prototype.l=function(g){e(g,0)};c.prototype.v=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var l=
g[h];g[h]=null;try{l()}catch(m){this.o(m)}}}this.g=null};c.prototype.o=function(g){this.l(function(){throw g;})};b.prototype.o=function(){function g(m){return function(n){l||(l=!0,m.call(h,n))}}var h=this,l=!1;return{resolve:g(this.N),reject:g(this.v)}};b.prototype.N=function(g){if(g===this)this.v(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.O(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.L(g):this.C(g)}};
b.prototype.L=function(g){var h=void 0;try{h=g.then}catch(l){this.v(l);return}typeof h=="function"?this.R(h,g):this.C(g)};b.prototype.v=function(g){this.B(2,g)};b.prototype.C=function(g){this.B(1,g)};b.prototype.B=function(g,h){if(this.g!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.l=h;this.g===2&&this.U();this.F()};b.prototype.U=function(){var g=this;e(function(){if(g.J()){var h=fa.console;typeof h!=="undefined"&&h.error(g.l)}},1)};b.prototype.J=
function(){if(this.H)return!1;var g=fa.CustomEvent,h=fa.Event,l=fa.dispatchEvent;if(typeof l==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=fa.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.l;return l(g)};b.prototype.F=function(){if(this.j!=null){for(var g=0;g<this.j.length;++g)f.j(this.j[g]);this.j=null}};var f=new c;
b.prototype.O=function(g){var h=this.o();g.sa(h.resolve,h.reject)};b.prototype.R=function(g,h){var l=this.o();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};b.prototype.then=function(g,h){function l(x,w){return typeof x=="function"?function(L){try{m(x(L))}catch(O){n(O)}}:w}var m,n,u=new b(function(x,w){m=x;n=w});this.sa(l(g,m),l(h,n));return u};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.sa=function(g,h){function l(){switch(m.g){case 1:g(m.l);break;case 2:h(m.l);
break;default:throw Error("Unexpected state: "+m.g);}}var m=this;this.j==null?f.j(l):this.j.push(l);this.H=!0};b.resolve=d;b.reject=function(g){return new b(function(h,l){l(g)})};b.race=function(g){return new b(function(h,l){for(var m=v(g),n=m.next();!n.done;n=m.next())d(n.value).sa(h,l)})};b.all=function(g){var h=v(g),l=h.next();return l.done?d([]):new b(function(m,n){function u(L){return function(O){x[L]=O;w--;w==0&&m(x)}}var x=[],w=0;do x.push(void 0),w++,d(l.value).sa(u(x.length-1),n),l=h.next();
while(!l.done)})};return b});function sa(a,b){return Object.prototype.hasOwnProperty.call(a,b)}r("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});r("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
r("WeakMap",function(a){function b(l){this.g=(h+=Math.random()+1).toString();if(l){l=v(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function e(l){if(!sa(l,g)){var m=new c;da(l,g,{value:m})}}function f(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),
n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(u){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(l,m){if(!d(l))throw Error("Invalid WeakMap key");e(l);if(!sa(l,g))throw Error("WeakMap key fail: "+l);l[g][this.g]=m;return this};b.prototype.get=function(l){return d(l)&&sa(l,g)?l[g][this.g]:void 0};b.prototype.has=function(l){return d(l)&&sa(l,
g)&&sa(l[g],this.g)};b.prototype.delete=function(l){return d(l)&&sa(l,g)&&sa(l[g],this.g)?delete l[g][this.g]:!1};return b});
r("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,l){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.previous;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})}function d(h,l){var m=l&&typeof l;m=="object"||m=="function"?f.has(l)?m=f.get(l):(m=""+ ++g,f.set(l,m)):m="p_"+l;var n=h[0][m];if(n&&sa(h[0],m))for(h=0;h<n.length;h++){var u=n[h];if(l!==l&&u.key!==u.key||l===u.key)return{id:m,list:n,index:h,entry:u}}return{id:m,
list:n,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=v(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if(l.get(h)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=
4||n.value[1]!="t"||!m.next().done?!1:!0}catch(u){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,l){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:l},m.list.push(m.entry),this[1].previous.next=m.entry,this[1].previous=m.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,h.call(l,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});
r("Set",function(a){function b(c){this.g=new Map;if(c){c=v(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(v([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});r("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push(b[d]);return c}});r("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
r("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});function ta(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}
r("String.prototype.includes",function(a){return a?a:function(b,c){return ta(this,b,"includes").indexOf(b,c||0)!==-1}});r("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:aa();var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
r("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push([d,b[d]]);return c}});r("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});r("Number.MAX_SAFE_INTEGER",ba(9007199254740991));r("Number.MIN_SAFE_INTEGER",ba(-9007199254740991));r("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
r("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});r("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ta(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
function ua(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}r("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}});r("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
r("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});r("Array.prototype.keys",function(a){return a?a:function(){return ua(this,aa())}});r("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}});r("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
function va(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]}r("Array.prototype.at",function(a){return a?a:va});function wa(a){return a?a:va}r("Int8Array.prototype.at",wa);r("Uint8Array.prototype.at",wa);r("Uint8ClampedArray.prototype.at",wa);r("Int16Array.prototype.at",wa);r("Uint16Array.prototype.at",wa);r("Int32Array.prototype.at",wa);r("Uint32Array.prototype.at",wa);r("Float32Array.prototype.at",wa);r("Float64Array.prototype.at",wa);
r("String.prototype.at",function(a){return a?a:va});r("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var xa=xa||{},y=this||self;function ya(a,b){var c=za("CLOSURE_FLAGS");a=c&&c[a];return a!=null?a:b}function za(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function Aa(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function Ba(a){var b=Aa(a);return b=="array"||b=="object"&&typeof a.length=="number"}function Ca(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}var Da="closure_uid_"+(Math.random()*1E9>>>0),Ea=0;
function Fa(a,b,c){return a.call.apply(a.bind,arguments)}function Ga(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Ha(a,b,c){Ha=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Fa:Ga;return Ha.apply(null,arguments)}
function Ia(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function Ja(a){(0,eval)(a)}function Ka(a){return a}function z(a,b){function c(){}c.prototype=b.prototype;a.ba=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.uc=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function La(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,La);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.g=!0}z(La,Error);La.prototype.name="CustomError";function Ma(a){y.setTimeout(function(){throw a;},0)};var Na=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var Oa=ya(610401301,!1),Pa=ya(748402147,ya(1,!1));function Qa(){var a=y.navigator;return a&&(a=a.userAgent)?a:""}var Ra,Sa=y.navigator;Ra=Sa?Sa.userAgentData||null:null;function Ta(a){if(!Oa||!Ra)return!1;for(var b=0;b<Ra.brands.length;b++){var c=Ra.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function A(a){return Qa().indexOf(a)!=-1};function Ua(){return Oa?!!Ra&&Ra.brands.length>0:!1}function Va(){return A("Firefox")||A("FxiOS")}function Ya(){return Ua()?Ta("Chromium"):(A("Chrome")||A("CriOS"))&&!(Ua()?0:A("Edge"))||A("Silk")};function Za(){return Oa?!!Ra&&!!Ra.platform:!1}function $a(){return A("iPhone")&&!A("iPod")&&!A("iPad")}function ab(){$a()||A("iPad")||A("iPod")};function bb(a,b){return Array.prototype.some.call(a,b,void 0)}function cb(a,b){b=Array.prototype.indexOf.call(a,b,void 0);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function db(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(Ba(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};A("Mobile");Za()||A("Macintosh");Za()||A("Windows");(Za()?Ra.platform==="Linux":A("Linux"))||Za()||A("CrOS");Za()||A("Android");$a();A("iPad");A("iPod");ab();Qa().toLowerCase().indexOf("kaios");Va();$a()||A("iPod");A("iPad");!A("Android")||Ya()||Va()||(Ua()?0:A("Opera"))||A("Silk");Ya();!A("Safari")||Ya()||(Ua()?0:A("Coast"))||(Ua()?0:A("Opera"))||(Ua()?0:A("Edge"))||(Ua()?Ta("Microsoft Edge"):A("Edg/"))||(Ua()?Ta("Opera"):A("OPR"))||Va()||A("Silk")||A("Android")||ab();var eb={},fb=null;var gb=typeof Uint8Array!=="undefined",hb=typeof btoa==="function",ib={},jb=typeof structuredClone!="undefined";function kb(a,b){if(b!==ib)throw Error("illegal external caller");this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}function lb(){return mb||(mb=new kb(null,ib))}var mb;function nb(a,b,c){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382[b]=c}function ob(a){return a.__closure__error__context__984382||{}};var qb=void 0;function rb(a,b){if(a!=null){var c;var d=(c=qb)!=null?c:qb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),nb(a,"severity","incident"),Ma(a))}};var sb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function tb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var ub=tb("jas",void 0,!0),vb=tb(void 0,"0di"),wb=tb(void 0,"1oa"),xb=tb(void 0,Symbol()),yb=tb(void 0,"0ubs"),zb=tb(void 0,"0actk"),Ab=tb("m_m","xc",!0);Math.max.apply(Math,oa(Object.values({Yb:1,Wb:2,Tb:4,hc:8,rc:16,cc:32,Gb:64,Rb:128,Pb:256,nc:512,Qb:1024,Sb:2048,dc:4096})));var Bb={yb:{value:0,configurable:!0,writable:!0,enumerable:!1}},Cb=Object.defineProperties,B=sb?ub:"yb",Db,Eb=[];Fb(Eb,7);Db=Object.freeze(Eb);function Gb(a,b){sb||B in a||Cb(a,Bb);a[B]|=b}function Fb(a,b){sb||B in a||Cb(a,Bb);a[B]=b}function Hb(a){Gb(a,34);return a};function Ib(){return typeof BigInt==="function"};var Jb={};function Kb(a,b){return b===void 0?a.g!==Lb&&!!(2&(a.D[B]|0)):!!(2&b)&&a.g!==Lb}var Lb={},Mb=Object.freeze({});function Nb(a){a.wc=!0;return a};var Pb=Nb(function(a){return typeof a==="number"}),Qb=Nb(function(a){return typeof a==="string"}),Rb=Nb(function(a){return typeof a==="boolean"}),Sb=Nb(function(a){return typeof a==="bigint"});var Tb=typeof y.BigInt==="function"&&typeof y.BigInt(0)==="bigint";function Ub(a){var b=a;if(Qb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Pb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Tb?BigInt(a):a=Rb(a)?a?"1":"0":Qb(a)?a.trim()||"0":String(a)}
var Vb=Nb(function(a){return Tb?Sb(a):Qb(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)}),ac=Nb(function(a){return Tb?a>=Wb&&a<=Xb:a[0]==="-"?Yb(a,Zb):Yb(a,$b)}),Zb=Number.MIN_SAFE_INTEGER.toString(),Wb=Tb?BigInt(Number.MIN_SAFE_INTEGER):void 0,$b=Number.MAX_SAFE_INTEGER.toString(),Xb=Tb?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Yb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var C=0,bc=0;function cc(a){var b=a>>>0;C=b;bc=(a-b)/4294967296>>>0}function dc(a){if(a<0){cc(0-a);var b=v(ec(C,bc));a=b.next().value;b=b.next().value;C=a>>>0;bc=b>>>0}else cc(a)}function fc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Ib()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+hc(c)+hc(a));return c}
function hc(a){a=String(a);return"0000000".slice(a.length)+a}function ic(){var a=C,b=bc;b&2147483648?Ib()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=v(ec(a,b)),a=b.next().value,b=b.next().value,a="-"+fc(a,b)):a=fc(a,b);return a}function ec(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var jc=typeof BigInt==="function"?BigInt.asIntN:void 0,kc=Number.isSafeInteger,lc=Number.isFinite,mc=Math.trunc;function nc(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function oc(a){return a.displayName||a.name||"unknown type name"}var pc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function qc(a){switch(typeof a){case "bigint":return!0;case "number":return lc(a);case "string":return pc.test(a);default:return!1}}
function rc(a){if(!lc(a))throw a=Error("enum"),nb(a,"severity","warning"),a;return a|0}function sc(a){return a==null?a:lc(a)?a|0:void 0}function tc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return lc(a)?a|0:void 0}function uc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}
function vc(a){a.indexOf(".");if(uc(a))return a;if(a.length<16)dc(Number(a));else if(Ib())a=BigInt(a),C=Number(a&BigInt(4294967295))>>>0,bc=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");bc=C=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),bc*=1E6,C=C*1E6+d,C>=4294967296&&(bc+=Math.trunc(C/4294967296),bc>>>=0,C>>>=0);b&&(b=v(ec(C,bc)),a=b.next().value,b=b.next().value,C=a,bc=b)}return ic()}
function wc(a){qc(a);a=mc(a);if(!kc(a)){dc(a);var b=C,c=bc;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:fc(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function xc(a){qc(a);a=mc(a);if(kc(a))a=String(a);else{var b=String(a);uc(b)?a=b:(dc(a),a=ic())}return a}function yc(a){return a==null||typeof a==="string"?a:void 0}
function zc(a,b,c,d){if(a!=null&&a[Ab]===Jb)return a;if(!Array.isArray(a))return c?d&2?b[vb]||(b[vb]=Ac(b)):new b:void 0;c=a[B]|0;d=c|d&32|d&2;d!==c&&Fb(a,d);return new b(a)}function Ac(a){a=new a;Hb(a.D);return a};function Bc(a){return a};function Cc(){}function Dc(a,b){for(var c in a)!isNaN(c)&&b(a,+c,a[c])}function Ec(a){var b=new Cc;Dc(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b}function Fc(a,b){b<100||rb(yb,1)};function Gc(a,b,c,d){var e=d!==void 0;d=!!d;var f=Ka(xb),g;!e&&sb&&f&&(g=a[f])&&Dc(g,Fc);f=[];var h=a.length;g=4294967295;var l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var u=h&&a[h-1];u!=null&&typeof u==="object"&&u.constructor===Object?(h--,g=h):u=void 0;if(m&&!(b&128)&&!e){l=!0;var x;g=((x=Hc)!=null?x:Bc)(g-n,n,a,u,void 0)+n}}b=void 0;for(x=0;x<h;x++){var w=a[x];if(w!=null&&(w=c(w,d))!=null)if(m&&x>=g){var L=x-n,O=void 0;((O=b)!=null?O:b={})[L]=w}else f[x]=w}if(u)for(var Q in u)h=u[Q],h!=
null&&(h=c(h,d))!=null&&(x=+Q,w=void 0,m&&!Number.isNaN(x)&&(w=x+n)<g?f[w]=h:(x=void 0,((x=b)!=null?x:b={})[Q]=h));b&&(l?f.push(b):f[g]=b);e&&Ka(xb)&&(a=(c=Ka(xb))?a[c]:void 0)&&a instanceof Cc&&(f[xb]=Ec(a));return f}
function Ic(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return ac(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[B]|0;return a.length===0&&b&1?void 0:Gc(a,b,Ic)}if(a!=null&&a[Ab]===Jb)return Jc(a);if(a instanceof kb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(hb){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):
b);b=btoa(c)}else{c===void 0&&(c=0);if(!fb){fb={};d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");e=["+/=","+/","-_=","-_.","-_"];for(var f=0;f<5;f++){var g=d.concat(e[f].split(""));eb[f]=g;for(var h=0;h<g.length;h++){var l=g[h];fb[l]===void 0&&(fb[l]=h)}}}c=eb[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=g=0;g<b.length-2;g+=3){var m=b[g],n=b[g+1];l=b[g+2];h=c[m>>2];m=c[(m&3)<<4|n>>4];n=c[(n&15)<<2|l>>6];l=c[l&63];d[f++]=""+h+m+n+l}h=0;l=e;switch(b.length-g){case 2:h=
b[g+1],l=c[(h&15)<<2]||e;case 1:b=b[g],d[f]=""+c[b>>2]+c[(b&3)<<4|h>>4]+l+e}b=d.join("")}a=a.g=b}return a}return}return a}var Kc=jb?structuredClone:function(a){return Gc(a,0,Ic)},Hc;function Jc(a){a=a.D;return Gc(a,a[B]|0,Ic)};function D(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[B]|0;if(Pa&&1&e)throw Error("rfarr");2048&e&&!(2&e)&&Lc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||Fb(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var l in h)f=
+l,f<g&&(c[f+b]=h[l],delete h[l]);e=e&-8380417|(g&1023)<<13;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("spvt");e=e&-8380417|(l&1023)<<13}}}e|=64;d===0&&(e|=2048);Fb(a,e);return a}function Lc(){if(Pa)throw Error("carr");rb(zb,5)};function Mc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[B]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Nc(a,c,!1,b&&!(c&16)):(Gb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Ab]===Jb)return b=a.D,c=b[B]|0,Kb(a,c)?a:Oc(a,b,c)?Pc(a,b):Nc(b,c);if(a instanceof kb)return a}function Pc(a,b,c){a=new a.constructor(b);c&&(a.g=Lb);a.j=Lb;return a}function Nc(a,b,c,d){d!=null||(d=!!(34&b));a=Gc(a,b,Mc,d);d=32;c&&(d|=2);b=b&8380609|d;Fb(a,b);return a}
function Qc(a){var b=a.D,c=b[B]|0;return Kb(a,c)?Oc(a,b,c)?Pc(a,b,!0):new a.constructor(Nc(b,c,!1)):a}function Rc(a){if(a.g!==Lb)return!1;var b=a.D;b=Nc(b,b[B]|0);Gb(b,2048);a.D=b;a.g=void 0;a.j=void 0;return!0}function Tc(a,b){b===void 0&&(b=a[B]|0);b&32&&!(b&4096)&&Fb(a,b|4096)}function Oc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(Fb(b,c|2),a.g=Lb,!0):!1};var Uc=Ub(0),Vc={};function E(a,b,c,d,e){Object.isExtensible(a);b=Wc(a.D,b,c,e);if(b!==null||d&&a.j!==Lb)return b}function Wc(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}
function Xc(a,b,c){if(!Rc(a)&&Kb(a,a.D[B]|0))throw Error();var d=a.D;Yc(d,d[B]|0,b,c);return a}function Yc(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[B]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}
function Zc(a,b,c,d,e,f,g,h){var l=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?$c(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==l&&Fb(a,b),Object.freeze(a)):(f===2&&$c(b)&&(a=Array.prototype.slice.call(a),l=0,b=ad(b,d),d=Yc(c,d,e,a)),$c(b)||(h||(b|=16),b!==l&&Fb(a,b)));2&b||!(4096&b||16&b)||Tc(c,d);return a}function bd(a,b){a=Wc(a,b);return Array.isArray(a)?a:Db}function cd(a,b){2&b&&(a|=2);return a|1}function $c(a){return!!(2&a)&&!!(4&a)||!!(256&a)}
function dd(a){return a==null?a:typeof a==="string"?a?new kb(a,ib):lb():a.constructor===kb?a:gb&&a!=null&&a instanceof Uint8Array?a.length?new kb(new Uint8Array(a),ib):lb():void 0}function ed(a,b,c){return fd(a,b)===c?c:-1}
function fd(a,b){a=a.D;if(sb){var c;var d=(c=a[wb])!=null?c:a[wb]=new Map}else wb in a?d=a[wb]:(c=new Map,Object.defineProperty(a,wb,{value:c}),d=c);c=d;d=void 0;var e=c.get(b);if(e==null){for(var f=e=0;f<b.length;f++){var g=b[f];Wc(a,g)!=null&&(e!==0&&(d=Yc(a,d,e)),e=g)}c.set(b,e)}return e}function gd(a,b,c,d){var e=!1;d=Wc(a,d,void 0,function(f){var g=zc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!Kb(d)&&Tc(a,b),d}
function hd(a,b,c){a=a.D;return gd(a,a[B]|0,b,c)||b[vb]||(b[vb]=Ac(b))}function id(a,b,c){var d=a.D,e=d[B]|0;b=gd(d,e,b,c);if(b==null)return b;e=d[B]|0;if(!Kb(a,e)){var f=Qc(b);f!==b&&(Rc(a)&&(d=a.D,e=d[B]|0),b=f,e=Yc(d,e,c,b),Tc(d,e))}return b}
function jd(a,b,c){var d=void 0===Mb?2:4,e=a.D,f=e;e=e[B]|0;var g=Kb(a,e),h=g?1:d;d=h===3;var l=!g;(h===2||l)&&Rc(a)&&(f=a.D,e=f[B]|0);a=bd(f,c);var m=a===Db?7:a[B]|0,n=cd(m,e);if(g=!(4&n)){var u=a,x=e,w=!!(2&n);w&&(x|=2);for(var L=!w,O=!0,Q=0,Wa=0;Q<u.length;Q++){var Xa=zc(u[Q],b,!1,x);if(Xa instanceof b){if(!w){var pb=Kb(Xa);L&&(L=!pb);O&&(O=pb)}u[Wa++]=Xa}}Wa<Q&&(u.length=Wa);n|=4;n=O?n&-4097:n|4096;n=L?n|8:n&-9}n!==m&&(Fb(a,n),2&n&&Object.freeze(a));if(l&&!(8&n||!a.length&&(h===1||(h!==4?0:2&
n||!(16&n)&&32&e)))){$c(n)&&(a=Array.prototype.slice.call(a),n=ad(n,e),e=Yc(f,e,c,a));b=a;l=n;for(m=0;m<b.length;m++)u=b[m],n=Qc(u),u!==n&&(b[m]=n);l|=8;n=l=b.length?l|4096:l&-4097;Fb(a,n)}return a=Zc(a,n,f,e,c,h,g,d)}function kd(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+oc(b)+" but got "+(d&&oc(d.constructor)));}else d=void 0;Xc(a,c,d);d&&!Kb(d)&&Tc(a.D);return a}function ad(a,b){return a=(2&b?a|2:a&-3)&-273}
function ld(a,b){var c=c===void 0?!1:c;a=E(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c}function md(a,b,c){c=c===void 0?0:c;var d;return(d=tc(E(a,b)))!=null?d:c}function nd(a,b){var c=c===void 0?Uc:c;a=E(a,b);b=typeof a;a!=null&&(b==="bigint"?a=Ub(jc(64,a)):qc(a)?b==="string"?(b=mc(Number(a)),kc(b)?a=Ub(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Ib()?Ub(jc(64,BigInt(a))):Ub(vc(a)))):a=kc(a)?Ub(wc(a)):Ub(xc(a)):a=void 0);return a!=null?a:c}
function od(a,b){var c=c===void 0?"":c;var d;return(d=yc(E(a,b)))!=null?d:c}function pd(a,b){var c=c===void 0?0:c;var d;return(d=sc(E(a,b)))!=null?d:c}function qd(a,b){return yc(E(a,b,void 0,Vc))}function rd(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Xc(a,b,c)};function F(a,b,c){this.D=D(a,b,c)}F.prototype.toJSON=function(){return Jc(this)};function sd(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Gb(b,32);return new a(b)}F.prototype.clone=function(){var a=this.D,b=a[B]|0;return Oc(this,a,b)?Pc(this,a,!0):new this.constructor(Nc(a,b,!1))};F.prototype[Ab]=Jb;F.prototype.toString=function(){return this.D.toString()};
function td(a,b){if(b==null)b=a.constructor,b=b[vb]||(b[vb]=Ac(b));else{a=a.constructor;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();b=new a(Hb(b))}return b};function ud(a){return function(b){return sd(a,b)}};function vd(a){this.D=D(a)}t(vd,F);vd.prototype.getTypeName=function(){return od(this,1).split("/").pop()};var wd=function(a){return Nb(function(b){return b instanceof a&&!Kb(b)})}(vd);function xd(){var a=yd("[]"),b=zd;this.key="45696263";this.defaultValue=a;this.g=b;this.flagNameForDebugging=void 0}
xd.prototype.ctor=function(a){if(typeof a==="string"&&a)return sd(this.g,a);if(!wd(a))return this.defaultValue.clone();var b;try{var c,d=this.g,e=(c=a.getTypeName())!=null?c:"";if(od(a,1).split("/").pop()!=e)var f=null;else{var g=typeof d==="function"?d:d.constructor,h=a.D,l=h[B]|0,m=Wc(h,2);Rc(a)&&(h=a.D,l=h[B]|0);a=h;if(m!=null&&!(Array.isArray(m)||m!=null&&m[Ab]===Jb))throw Error("saw an invalid value of type '"+Aa(m)+"' in the Any.value field");var n=zc(m,g,!0,l);if(!(n instanceof g))throw Error("incorrect type in any value: got "+
n.constructor.displayName+", expected "+g.displayName);(g=!!(2&l))||(n=Qc(n));m!==n&&(Yc(a,l,2,n),g||Tc(a));f=n}}catch(u){f=null}return(b=f)!=null?b:this.defaultValue.clone()};function Ad(a){this.D=D(a)}t(Ad,F);var Bd=[1,2];function Cd(a){this.D=D(a)}t(Cd,F);var Dd=[2,3,4,5,6,8];function Ed(a){this.D=D(a)}t(Ed,F);Ed.prototype.Sa=function(){var a=E(this,3,void 0,void 0,dd);return a==null?lb():a};function Fd(a){this.D=D(a)}t(Fd,F);var Gd=ud(Fd);function zd(a){this.D=D(a)}t(zd,F);var yd=ud(zd);function Hd(a,b){this.K=a|0;this.I=b|0}function Id(a){return a.I*4294967296+(a.K>>>0)}q=Hd.prototype;q.isSafeInteger=function(){var a=this.I>>21;return a==0||a==-1&&!(this.K==0&&this.I==-2097152)};
q.toString=function(a){a=a||10;if(a<2||36<a)throw Error("radix out of range: "+a);if(this.isSafeInteger()){var b=Id(this);return a==10?""+b:b.toString(a)}b=14-(a>>2);var c=Math.pow(a,b),d=G(c,c/4294967296);c=this.div(d);var e=Math,f=e.abs;d=c.multiply(d);d=this.add(Jd(d));e=f.call(e,Id(d));f=a==10?""+e:e.toString(a);f.length<b&&(f="0000000000000".slice(f.length-b)+f);e=Id(c);return(a==10?e:e.toString(a))+f};function Kd(a){return a.K==0&&a.I==0}q.T=function(){return this.K^this.I};
q.equals=function(a){return a==null?!1:this.K==a.K&&this.I==a.I};q.compare=function(a){return this.I==a.I?this.K==a.K?0:this.K>>>0>a.K>>>0?1:-1:this.I>a.I?1:-1};function Jd(a){var b=~a.K+1|0;return G(b,~a.I+!b|0)}q.add=function(a){var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=a.I>>>16,f=a.I&65535,g=a.K>>>16;a=(this.K&65535)+(a.K&65535);g=(a>>>16)+(d+g);d=g>>>16;d+=c+f;return G((g&65535)<<16|a&65535,((d>>>16)+(b+e)&65535)<<16|d&65535)};
q.multiply=function(a){if(Kd(this))return this;if(Kd(a))return a;var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=this.K&65535,f=a.I>>>16,g=a.I&65535,h=a.K>>>16;a=a.K&65535;var l=e*a;var m=(l>>>16)+d*a;var n=m>>>16;m=(m&65535)+e*h;n+=m>>>16;n+=c*a;var u=n>>>16;n=(n&65535)+d*h;u+=n>>>16;n=(n&65535)+e*g;u=u+(n>>>16)+(b*a+c*h+d*g+e*f)&65535;return G((m&65535)<<16|l&65535,u<<16|n&65535)};
q.div=function(a){if(Kd(a))throw Error("division by zero");if(this.I<0){if(this.equals(Ld)){if(a.equals(Md)||a.equals(Nd))return Ld;if(a.equals(Ld))return Md;var b=this.I;b=G(this.K>>>1|b<<31,b>>1);b=b.div(a).shiftLeft(1);if(b.equals(Od))return a.I<0?Md:Nd;var c=a.multiply(b);c=this.add(Jd(c));return b.add(c.div(a))}return a.I<0?Jd(this).div(Jd(a)):Jd(Jd(this).div(a))}if(Kd(this))return Od;if(a.I<0)return a.equals(Ld)?Od:Jd(this.div(Jd(a)));b=Od;for(c=this;c.compare(a)>=0;){var d=Math.max(1,Math.floor(Id(c)/
Id(a))),e=Math.ceil(Math.log(d)/Math.LN2);e=e<=48?1:Math.pow(2,e-48);for(var f=Pd(d),g=f.multiply(a);g.I<0||g.compare(c)>0;)d-=e,f=Pd(d),g=f.multiply(a);Kd(f)&&(f=Md);b=b.add(f);c=c.add(Jd(g))}return b};q.and=function(a){return G(this.K&a.K,this.I&a.I)};q.or=function(a){return G(this.K|a.K,this.I|a.I)};q.xor=function(a){return G(this.K^a.K,this.I^a.I)};q.shiftLeft=function(a){a&=63;if(a==0)return this;var b=this.K;return a<32?G(b<<a,this.I<<a|b>>>32-a):G(0,b<<a-32)};
function Pd(a){return a>0?a>=0x7fffffffffffffff?Qd:new Hd(a,a/4294967296):a<0?a<=-0x7fffffffffffffff?Ld:Jd(new Hd(-a,-a/4294967296)):Od}function G(a,b){return new Hd(a,b)}var Od=G(0,0),Md=G(1,0),Nd=G(-1,-1),Qd=G(4294967295,2147483647),Ld=G(0,2147483648);function Rd(a,b){b=b===void 0?window:b;b=b===void 0?window:b;return(b=b.WIZ_global_data)&&a in b?b[a]:null};var Sd;
function Td(){var a=Gd("["+Rd("TSDtV",window).substring(4));if(a=jd(a,Ed,1)[0])for(var b=v(jd(a,Cd,2)),c=b.next();!c.done;c=b.next()){c=c.value;var d=c.D;if(gd(d,d[B]|0,vd,ed(c,Dd,6))!==void 0)throw Error();}if(a)for(b={},c=v(jd(a,Cd,2)),d=c.next();!d.done;d=c.next()){var e=d.value;d=nd(e,1).toString();switch(fd(e,Dd)){case 3:b[d]=ld(e,ed(e,Dd,3));break;case 2:var f=nd(e,ed(e,Dd,2));Vb(f);ac(f);f=ac(f)?Number(f):String(f);b[d]=f;break;case 4:f=void 0;var g=e;var h=ed(e,Dd,4);e=void 0;e=e===void 0?
0:e;g=(f=E(g,h,void 0,void 0,nc))!=null?f:e;b[d]=g;break;case 5:b[d]=od(e,ed(e,Dd,5));break;case 6:b[d]=id(e,vd,ed(e,Dd,6));break;case 8:f=hd(e,Ad,ed(e,Dd,8));switch(fd(f,Bd)){case 1:b[d]=od(f,ed(f,Bd,1));break;default:throw Error("case "+fd(f,Bd));}break;default:throw Error("case "+fd(e,Dd));}}else b={};this.g=b;this.j=a?a.Sa():null}Td.prototype.Sa=p("j");function Ud(a){this.D=D(a)}t(Ud,F);var Vd=new xd;function Wd(a){this.D=D(a)}t(Wd,F);var Xd=function(a){return function(){return a[vb]||(a[vb]=Ac(a))}}(Wd);Object.create(null);function H(){}H.prototype.equals=function(a){return I(this,a)};H.prototype.T=function(){return Yd(this)};H.prototype.toString=function(){return J(Zd(K($d(this))))+"@"+J((this.T()>>>0).toString(16))};function ae(a){return a!=null}H.prototype.A=["java.lang.Object",0];function be(){}t(be,H);function ce(a){de(a);ee(a)}function fe(a,b){de(a);a.j=b;ee(a)}function M(a,b){a.g=b;ge(b,a)}function ee(a){he(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(N(a.g,he,ie)):N(a.g,he,ie).stack=Error().stack);a.l=null}be.prototype.toString=function(){var a=Zd(K($d(this))),b=this.j;return b==null?a:J(a)+": "+J(b)};
function je(a){if(a!=null){var b=a.hb;if(b!=null)return b}a instanceof TypeError?b=ke():(b=new le,ce(b),M(b,Error(b)));b.j=a==null?"null":a.toString();M(b,a);return b}function de(a){a.l=me([0],ne,oe)}function pe(a){return a instanceof be}be.prototype.A=["java.lang.Throwable",0];function qe(){}t(qe,be);qe.prototype.A=["java.lang.Exception",0];function P(){}t(P,qe);P.prototype.A=["java.lang.RuntimeException",0];function re(){}t(re,P);function se(){var a=new re;ce(a);M(a,Error(a));return a}function te(a){var b=new re;fe(b,a);M(b,Error(b));return b}re.prototype.A=["java.lang.IndexOutOfBoundsException",0];var ue;function ve(){ve=k();for(var a=me([256],we,xe),b=0;b<256;b=b+1|0)R(a,b,ye(b-128|0));ue=a};function ze(){}t(ze,P);ze.prototype.A=["java.lang.ArithmeticException",0];function Ae(){}t(Ae,P);Ae.prototype.A=["java.lang.ArrayStoreException",0];function Be(){}t(Be,P);Be.prototype.A=["java.lang.ClassCastException",0];function Ce(){}t(Ce,P);function De(a){var b=new Ce;fe(b,a);M(b,Error(b));return b}Ce.prototype.A=["java.lang.IllegalArgumentException",0];function Ee(){}t(Ee,P);function Fe(){var a=new Ee;ce(a);M(a,Error(a));return a}Ee.prototype.A=["java.lang.IllegalStateException",0];function le(){}t(le,P);le.prototype.A=["java.lang.JsException",0];function Ge(){}t(Ge,le);function ke(){var a=new Ge;ce(a);M(a,new TypeError(a));return a}Ge.prototype.A=["java.lang.NullPointerException",0];function He(){}t(He,re);function Ie(a){var b=new He;fe(b,a);M(b,Error(b));return b}He.prototype.A=["java.lang.StringIndexOutOfBoundsException",0];function Je(){}t(Je,P);function Ke(){var a=new Je;ce(a);M(a,Error(a));return a}Je.prototype.A=["java.util.ConcurrentModificationException",0];function Le(){}t(Le,P);function Me(){var a=new Le;ce(a);M(a,Error(a));return a}Le.prototype.A=["java.util.NoSuchElementException",0];function Ne(){}var Oe;t(Ne,H);Ne.prototype.A=["java.lang.Number",0];function Pe(){}t(Pe,Ne);Pe.prototype.A=["java.lang.Double",0];function Qe(a){return Pd(a)}function Re(a){if(!isFinite(a))throw a=new ze,ce(a),M(a,Error(a)),a.g;return a|0}function Se(a){return Math.max(Math.min(a,2147483647),-2147483648)|0};function Te(){}t(Te,H);Te.prototype.A=["java.lang.Boolean",0];function N(a,b,c){a==null||b(a)||(b=J(Zd(Ue(a)))+" cannot be cast to "+J(Zd(K(c))),Ve(b));return a};function $d(a){return a.constructor}function We(a,b,c){if(Object.prototype.hasOwnProperty.call(a.prototype,b))return a.prototype[b];c=c();return a.prototype[b]=c};function I(a,b){return Object.is(a,b)||a==null&&b==null};function Xe(a){switch(S(typeof a)){case "string":for(var b=0,c=0;c<a.length;c=c+1|0){b=(b<<5)-b;var d=a,e=c;Ye(e,d.length);b=b+d.charCodeAt(e)|0}return b;case "number":return a=S(a),Se(a);case "boolean":return S(a)?1231:1237;default:return a==null?0:Yd(a)}}var Ze=0;function Yd(a){return a.La||(Object.defineProperties(a,{La:{value:Ze=Ze+1|0,enumerable:!1}}),a.La)};function $e(a,b){return a.equals?a.equals(b):Object.is(a,b)}function af(a){return a.T?a.T():Xe(a)}function Ue(a){switch(S(typeof a)){case "number":return K(Pe);case "boolean":return K(Te);case "string":return K(bf);case "function":return K(cf)}if(a instanceof Hd)a=K(df);else if(a instanceof H)a=K($d(a));else if(Array.isArray(a))a=(a=a.W)?K(a.ha,a.ga):K(H,1);else if(a!=null)a=K(ef);else throw new TypeError("null.getClass()");return a};function cf(){}cf.prototype.A=["<native function>",1];function ef(){}t(ef,H);ef.prototype.A=["<native object>",0];function ff(){}t(ff,P);function T(){var a=new ff;ce(a);M(a,Error(a));return a}ff.prototype.A=["java.lang.UnsupportedOperationException",0];function U(a,b){return I(a,b)||a!=null&&$e(a,b)}function gf(a){return a!=null?af(a):0}function hf(a){if(a==null)throw ke().g;};function we(){this.ca=0}t(we,Ne);function jf(a){a>-129&&a<128?(ve(),a=ue[a+128|0]):a=ye(a);return a}function ye(a){var b=new we;b.ca=a;return b}we.prototype.equals=function(a){return xe(a)&&N(a,xe,we).ca==this.ca};we.prototype.T=p("ca");we.prototype.toString=function(){return""+this.ca};function xe(a){return a instanceof we}we.prototype.A=["java.lang.Integer",0];function df(){}t(df,Ne);df.prototype.A=["java.lang.Long",0];function kf(){}t(kf,H);q=kf.prototype;q.add=function(){throw T().g;};q.Aa=function(a){S(a);var b=!1;for(a=a.G();a.g();){var c=a.j();b=!!(+b|+this.add(c))}};q.clear=function(){for(var a=this.G();a.g();)a.j(),a.l()};q.contains=function(a){return lf(this,a,!1)};q.Ca=function(a){S(a);for(a=a.G();a.g();){var b=a.j();if(!this.contains(b))return!1}return!0};q.remove=function(a){return lf(this,a,!0)};q.removeAll=function(a){S(a);for(var b=!1,c=this.G();c.g();){var d=c.j();a.contains(d)&&(c.l(),b=!0)}return b};
q.fa=function(){return mf(this,Array(this.size()))};q.la=function(a){return mf(this,a)};q.toString=function(){for(var a=nf("[","]"),b=this.G();b.g();){var c=b.j();of(a,I(c,this)?"(this Collection)":J(c))}return a.toString()};function lf(a,b,c){for(a=a.G();a.g();){var d=a.j();if(U(b,d))return c&&a.l(),!0}return!1}q.Wa=function(){return this.fa()};q.A=["java.util.AbstractCollection",0];function pf(){}function qf(){var a=new rf;a.j=1;a.g=1;return sf(a,tf,uf)}function vf(a){return wf(a.slice(0,a.length))}function sf(){return wf(ra.apply(0,arguments))}function xf(a){return a!=null&&!!a.na}pf.prototype.na=!0;pf.prototype.A=["java.util.List",1];function yf(){}t(yf,kf);q=yf.prototype;q.add=function(a){this.qa(this.size(),a);return!0};q.qa=function(){throw T().g;};q.Ba=function(a,b){S(b);for(b=b.G();b.g();){var c=b.j(),d=void 0;this.qa((d=a,a=a+1|0,d),c)}};q.clear=function(){this.Ua(0,this.size())};q.equals=function(a){if(I(a,this))return!0;if(!xf(a))return!1;a=N(a,xf,pf);if(this.size()!=a.size())return!1;a=a.G();for(var b=this.G();b.g();){var c=b.j(),d=a.j();if(!U(c,d))return!1}return!0};
q.T=function(){zf();for(var a=1,b=this.G();b.g();){var c=b.j();a=Math.imul(31,a)+gf(c)|0}return a};q.indexOf=function(a){for(var b=0,c=this.size();b<c;b=b+1|0)if(U(a,this.Y(b)))return b;return-1};q.G=function(){var a=new Af;a.C=this;a.o=0;a.v=-1;return a};q.lastIndexOf=function(a){for(var b=this.size()-1|0;b>-1;b=b-1|0)if(U(a,this.Y(b)))return b;return-1};q.Fa=function(a){var b=new Bf;b.C=this;b.o=0;b.v=-1;Cf(a,this.size());b.o=a;return b};q.Ia=function(){throw T().g;};
q.Ua=function(a,b){for(var c=this.Fa(a);a<b;a=a+1|0)c.j(),c.l()};q.na=!0;q.A=["java.util.AbstractList",0];function Df(){}t(Df,yf);q=Df.prototype;q.Aa=function(a){this.Ba(this.g.length,a)};q.contains=function(a){return this.indexOf(a)!=-1};q.Y=function(a){Ef(a,this.g.length);return this.g[a]};q.indexOf=function(a){a:{for(var b=0,c=this.g.length;b<c;b=b+1|0)if(U(a,this.g[b])){a=b;break a}a=-1}return a};q.G=function(){var a=new Ff;a.C=this;a.o=0;a.v=-1;return a};q.lastIndexOf=function(a){a:{for(var b=this.g.length-1|0;b>=0;b=b-1|0)if(U(a,this.g[b])){a=b;break a}a=-1}return a};
q.Ia=function(a){this.Y(a);this.g.splice(a,1)};q.remove=function(a){a=this.indexOf(a);if(a==-1)return!1;this.g.splice(a,1);return!0};q.size=function(){return this.g.length};q.la=function(a){var b=this.g.length;a.length<b&&(a=Gf(Array(b),a));for(var c=0;c<b;c=c+1|0)R(a,c,this.g[c]);a.length>b&&R(a,b,null);return a};q.na=!0;q.A=["java.util.ArrayListBase",0];function Hf(){}t(Hf,Df);function If(){var a=new Hf;a.g=[];return a}q=Hf.prototype;q.add=function(a){this.g.push(a);return!0};q.qa=function(a,b){Cf(a,this.g.length);this.g.splice(a,0,b)};q.Ba=function(a,b){Cf(a,this.g.length);b=b.fa();var c=b.length;if(c!=0){var d=this.g.length+c|0;this.g.length=d;var e=a+c|0;Jf(this.g,a,this.g,e,d-e|0);Jf(b,0,this.g,a,c)}};q.fa=function(){var a=this.g,b=a.slice();b.W=a.W;b==null||Kf(b,H,ae,1)||(a=K(H,1),a=Zd(Ue(b))+" cannot be cast to "+Zd(a),Ve(a));return b};
q.Ua=function(a,b){var c=this.g.length;if(a<0||b>c)throw te("fromIndex: "+a+", toIndex: "+b+", size: "+c).g;if(a>b)throw De("fromIndex: "+a+" > toIndex: "+b).g;this.g.splice(a,b-a|0)};q.A=["java.util.ArrayList",0];function Ff(){this.v=this.o=0}t(Ff,H);Ff.prototype.g=function(){return this.o<this.C.g.length};Ff.prototype.j=function(){Lf(this.g());var a;this.v=(a=this.o,this.o=this.o+1|0,a);return this.C.g[this.v]};Ff.prototype.l=function(){Mf(this.v!=-1);var a=this.C,b=this.o=this.v;a.g.splice(b,1);this.v=-1};Ff.prototype.A=["java.util.ArrayListBase$1",0];function Nf(){}t(Nf,yf);q=Nf.prototype;q.contains=ba(!1);q.Y=function(a){Ef(a,0);return null};q.G=function(){return Of()};q.size=ba(0);q.A=["java.util.Collections$EmptyList",0];function Pf(){}var Qf;t(Pf,H);Pf.prototype.g=ba(!1);Pf.prototype.j=function(){throw Me().g;};Pf.prototype.l=function(){throw Fe().g;};function Rf(){Rf=k();Qf=new Pf}Pf.prototype.A=["java.util.Collections$EmptyListIterator",0];function Sf(){}t(Sf,H);Sf.prototype.g=function(){return this.o.g()};Sf.prototype.j=function(){return N(this.o.j(),V,W).P()};Sf.prototype.l=function(){this.o.l()};Sf.prototype.A=["java.util.AbstractMap$1$1",0];function W(){}function V(a){return a!=null&&!!a.xa}W.prototype.xa=!0;W.prototype.A=["java.util.Map$Entry",1];function Tf(){}function Uf(){var a=ra.apply(0,arguments);zf();if(a.length==0)a=Vf(Wf);else{var b=new Xf;b.g=Yf();for(var c=0;c<a.length;c=c+1|0)if(!b.add(S(a[c])))throw De("Duplicate element").g;a=Vf(b)}return a}function Zf(a){return a!=null&&!!a.oa}Tf.prototype.oa=!0;Tf.prototype.A=["java.util.Set",1];function $f(){}t($f,kf);q=$f.prototype;q.equals=function(a){if(I(a,this))return!0;if(!Zf(a))return!1;a=N(a,Zf,Tf);return a.size()!=this.size()?!1:this.Ca(a)};q.T=function(){return ag(this)};q.removeAll=function(a){S(a);var b=this.size();if(b<a.size())for(var c=this.G();c.g();){var d=c.j();a.contains(d)&&c.l()}else for(a=a.G();a.g();)c=a.j(),this.remove(c);return b!=this.size()};q.oa=!0;q.A=["java.util.AbstractSet",0];function bg(){}t(bg,$f);q=bg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ia(a)};q.G=function(){var a=this.g.aa().G(),b=new Sf;b.o=a;return b};q.remove=function(a){return this.g.ia(a)?(this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.A=["java.util.AbstractMap$1",0];function cg(){}t(cg,H);cg.prototype.g=function(){return this.o.g()};cg.prototype.j=function(){return N(this.o.j(),V,W).S()};cg.prototype.l=function(){this.o.l()};cg.prototype.A=["java.util.AbstractMap$2$1",0];function dg(){}t(dg,kf);q=dg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.Na(a)};q.G=function(){var a=this.g.aa().G(),b=new cg;b.o=a;return b};q.size=function(){return this.g.size()};q.A=["java.util.AbstractMap$2",0];function eg(){}t(eg,H);q=eg.prototype;q.P=p("j");q.S=p("g");q.Ma=function(a){var b=this.g;this.g=a;return b};q.equals=function(a){if(!V(a))return!1;a=N(a,V,W);return U(this.j,a.P())&&U(this.g,a.S())};q.T=function(){return gf(this.j)^gf(this.g)};q.toString=function(){return J(this.j)+"="+J(this.g)};q.xa=!0;q.A=["java.util.AbstractMap$AbstractEntry",0];function fg(){}t(fg,eg);function gg(a,b){var c=new fg;c.j=a;c.g=b;return c}fg.prototype.A=["java.util.AbstractMap$SimpleEntry",0];function hg(){}function ig(a){return a!=null&&!!a.Ka}hg.prototype.Ka=!0;hg.prototype.A=["java.util.Map",1];function jg(){}t(jg,H);q=jg.prototype;q.clear=function(){this.aa().clear()};q.ia=function(a){return kg(this,a,!1)!=null};q.Na=function(a){for(var b=this.aa().G();b.g();){var c=N(b.j(),V,W).S();if(U(a,c))return!0}return!1};function lg(a,b){var c=b.P();b=b.S();var d=a.get(c);return!U(b,d)||d==null&&!a.ia(c)?!1:!0}q.equals=function(a){if(I(a,this))return!0;if(!ig(a))return!1;a=N(a,ig,hg);if(this.size()!=a.size())return!1;for(a=a.aa().G();a.g();){var b=N(a.j(),V,W);if(!lg(this,b))return!1}return!0};
q.get=function(a){return mg(kg(this,a,!1))};q.T=function(){return ag(this.aa())};q.Ta=function(){var a=new bg;a.g=this;return a};q.X=function(){throw T().g;};q.remove=function(a){return mg(kg(this,a,!0))};q.size=function(){return this.aa().size()};q.toString=function(){for(var a=nf("{","}"),b=this.aa().G();b.g();){var c=N(b.j(),V,W);c=J(ng(this,c.P()))+"="+J(ng(this,c.S()));of(a,c)}return a.toString()};function ng(a,b){return I(b,a)?"(this Map)":J(b)}q.values=function(){var a=new dg;a.g=this;return a};
function mg(a){return a==null?null:a.S()}function kg(a,b,c){for(a=a.aa().G();a.g();){var d=N(a.j(),V,W);if(U(b,d.P()))return c&&(d=gg(d.P(),d.S()),a.l()),d}return null}q.Ka=!0;q.A=["java.util.AbstractMap",0];function og(){}t(og,H);og.prototype.toString=p("g");og.prototype.A=["java.lang.AbstractStringBuilder",0];function pg(){}t(pg,og);pg.prototype.A=["java.lang.StringBuilder",0];function qg(){}t(qg,H);function nf(a,b){var c=new qg;c.o=", ".toString();c.l=a.toString();c.j=b.toString();c.v=J(c.l)+J(c.j);return c}function of(a,b){if(a.g==null){var c=new pg,d=N(S(a.l),rg,bf);c.g=d;a.g=c}else c=a.g,c.g=J(c.g)+J(a.o);a=a.g;a.g=J(a.g)+J(b)}qg.prototype.toString=function(){return this.g==null?this.v:this.j.length==0?this.g.toString():J(this.g.toString())+J(this.j)};qg.prototype.A=["java.util.StringJoiner",0];function sg(){}t(sg,$f);sg.prototype.contains=ba(!1);sg.prototype.G=function(){return Of()};sg.prototype.size=ba(0);sg.prototype.A=["java.util.Collections$EmptySet",0];function tg(){}t(tg,H);q=tg.prototype;q.add=function(){throw T().g;};q.Aa=function(){throw T().g;};q.clear=function(){throw T().g;};q.contains=function(a){return this.g.contains(a)};q.Ca=function(a){return this.g.Ca(a)};q.G=function(){var a=this.g.G(),b=new ug;b.o=a;return b};q.remove=function(){throw T().g;};q.removeAll=function(){throw T().g;};q.size=function(){return this.g.size()};q.fa=function(){return this.g.fa()};q.la=function(a){return this.g.la(a)};q.toString=function(){return this.g.toString()};
q.Wa=function(){return this.fa()};q.A=["java.util.Collections$UnmodifiableCollection",0];function ug(){}t(ug,H);ug.prototype.g=function(){return this.o.g()};ug.prototype.j=function(){return this.o.j()};ug.prototype.l=function(){throw T().g;};ug.prototype.A=["java.util.Collections$UnmodifiableCollectionIterator",0];function vg(){}t(vg,tg);q=vg.prototype;q.qa=function(){throw T().g;};q.Ba=function(){throw T().g;};q.equals=function(a){return $e(this.j,a)};q.Y=function(a){return this.j.Y(a)};q.T=function(){return af(this.j)};q.indexOf=function(a){return this.j.indexOf(a)};q.lastIndexOf=function(a){return this.j.lastIndexOf(a)};q.Fa=function(a){a=this.j.Fa(a);var b=new wg;b.o=a;return b};q.Ia=function(){throw T().g;};q.na=!0;q.A=["java.util.Collections$UnmodifiableList",0];function wg(){}t(wg,ug);wg.prototype.A=["java.util.Collections$UnmodifiableListIterator",0];function xg(){}t(xg,tg);xg.prototype.equals=function(a){return $e(this.g,a)};xg.prototype.T=function(){return af(this.g)};xg.prototype.oa=!0;xg.prototype.A=["java.util.Collections$UnmodifiableSet",0];function yg(){}t(yg,vg);yg.prototype.A=["java.util.Collections$UnmodifiableRandomAccessList",0];var zg,Wf;function Of(){zf();return Rf(),Qf}function wf(a){zf();for(var b=0;b<a.length;b=b+1|0)S(a[b]);a.length==0?b=zg:(b=new Ag,S(a),b.g=a);a=new yg;a.g=b;a.j=b;return a}function Vf(a){zf();var b=new xg;b.g=a;return b}function ag(a){zf();var b=0;for(a=a.G();a.g();){var c=a.j();b=b+gf(c)|0}return b}function zf(){zf=k();zg=new Nf;Wf=new sg};function Bg(){}t(Bg,$f);q=Bg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return V(a)?lg(this.g,N(a,V,W)):!1};q.G=function(){var a=new Cg;a.o=this.g;a.F=a.o.l.G();a.v=a.F;a.C=Dg(a);a.H=a.o.j;return a};q.remove=function(a){return this.contains(a)?(a=N(a,V,W).P(),this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.A=["java.util.AbstractHashMap$EntrySet",0];function Cg(){this.C=!1;this.H=0}t(Cg,H);Cg.prototype.g=p("C");function Dg(a){if(a.v.g())return!0;if(!I(a.v,a.F))return!1;a.v=a.o.g.G();return a.v.g()}Cg.prototype.l=function(){Mf(this.B!=null);if(this.o.j!=this.H)throw Ke().g;this.B.l();this.B=null;this.C=Dg(this);this.H=this.o.j};Cg.prototype.j=function(){if(this.o.j!=this.H)throw Ke().g;Lf(this.g());this.B=this.v;var a=N(this.v.j(),V,W);this.C=Dg(this);return a};Cg.prototype.A=["java.util.AbstractHashMap$EntrySetIterator",0];function Eg(){this.j=0}t(Eg,jg);q=Eg.prototype;q.clear=function(){Fg(this)};function Fg(a){var b=new Gg;b.j=new Map;b.l=a;a.g=b;b=new Hg;b.g=new Map;b.o=a;a.l=b;Ig(a)}function Ig(a){a.j=a.j+1|0}q.ia=function(a){return rg(a)?this.l.g.has(a):Jg(a,Kg(this.g,a==null?0:af(a)))!=null};q.Na=function(a){return Lg(a,this.l)||Lg(a,this.g)};function Lg(a,b){for(b=b.G();b.g();){var c=N(b.j(),V,W),d=a;c=c.S();if(U(d,c))return!0}return!1}q.aa=function(){var a=new Bg;a.g=this;return a};
q.get=function(a){return rg(a)?this.l.g.get(a):mg(Jg(a,Kg(this.g,a==null?0:af(a))))};q.X=function(a,b){if(rg(a))a=Mg(this.l,a,b);else a:{var c=this.g,d=a==null?0:af(a),e=Kg(c,d);if(e.length==0)c.j.set(d,e);else if(d=Jg(a,e),d!=null){a=d.Ma(b);break a}R(e,e.length,gg(a,b));c.g=c.g+1|0;Ig(c.l);a=null}return a};q.remove=function(a){return rg(a)?Ng(this.l,a):Og(this.g,a)};q.size=function(){return this.g.g+this.l.l|0};q.A=["java.util.AbstractHashMap",0];function Pg(){this.o=0}t(Pg,H);Pg.prototype.g=function(){if(this.o<this.v.length)return!0;var a=this.B.next();return a.done?!1:(this.v=a.value[1],this.o=0,!0)};Pg.prototype.l=function(){Og(this.H,this.C.P());this.o!=0&&(this.o=this.o-1|0)};Pg.prototype.j=function(){var a;return this.C=this.v[a=this.o,this.o=this.o+1|0,a]};Pg.prototype.A=["java.util.InternalHashCodeMap$1",0];function Gg(){this.g=0}t(Gg,H);function Og(a,b){for(var c=b==null?0:af(b),d=Kg(a,c),e=0;e<d.length;e=e+1|0){var f=d[e];if(U(b,f.P()))return d.length==1?(d.length=0,a.j.delete(c)):d.splice(e,1),a.g=a.g-1|0,Ig(a.l),f.S()}return null}function Jg(a,b){for(var c=0;c<b.length;c++){var d=b[c];if(U(a,d.P()))return d}return null}Gg.prototype.G=function(){var a=new Pg;a.H=this;a.B=a.H.j.entries();a.o=0;a.v=[];a.C=null;return a};function Kg(a,b){a=a.j.get(b);return a==null?[]:a}
Gg.prototype.A=["java.util.InternalHashCodeMap",0];function Qg(){}t(Qg,H);Qg.prototype.g=function(){return!this.v.done};Qg.prototype.l=function(){Ng(this.o,this.H.value[0])};Qg.prototype.j=function(){this.H=this.v;this.v=this.C.next();var a=new Rg,b=this.H,c=this.o.j;a.j=this.o;a.g=b;a.l=c;return a};Qg.prototype.A=["java.util.InternalStringMap$1",0];function Sg(){}t(Sg,H);q=Sg.prototype;q.equals=function(a){if(!V(a))return!1;a=N(a,V,W);return U(this.P(),a.P())&&U(this.S(),a.S())};q.T=function(){return gf(this.P())^gf(this.S())};q.toString=function(){return J(this.P())+"="+J(this.S())};q.xa=!0;q.A=["java.util.AbstractMapEntry",0];function Rg(){this.l=0}t(Rg,Sg);Rg.prototype.P=function(){return this.g.value[0]};Rg.prototype.S=function(){return this.j.j!=this.l?this.j.g.get(this.g.value[0]):this.g.value[1]};Rg.prototype.Ma=function(a){return Mg(this.j,this.g.value[0],a)};Rg.prototype.A=["java.util.InternalStringMap$2",0];function Hg(){this.j=this.l=0}t(Hg,H);function Mg(a,b,c){var d=a.g.get(b);a.g.set(b,c===void 0?null:c);d===void 0?(a.l=a.l+1|0,Ig(a.o)):a.j=a.j+1|0;return d}function Ng(a,b){var c=a.g.get(b);c===void 0?a.j=a.j+1|0:(a.g.delete(b),a.l=a.l-1|0,Ig(a.o));return c}Hg.prototype.G=function(){var a=new Qg;a.o=this;a.C=a.o.g.entries();a.v=a.C.next();return a};Hg.prototype.A=["java.util.InternalStringMap",0];function Tg(){this.j=0}t(Tg,Eg);function Yf(){var a=new Tg;Fg(a);return a}Tg.prototype.A=["java.util.HashMap",0];function Xf(){}t(Xf,$f);q=Xf.prototype;q.add=function(a){return this.g.X(a,this)==null};q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ia(a)};q.G=function(){return this.g.Ta().G()};q.remove=function(a){return this.g.remove(a)!=null};q.size=function(){return this.g.size()};q.oa=!0;q.A=["java.util.HashSet",0];function Ug(){}var Vg;t(Ug,H);function Wg(a){var b=new Ug;b.g=a;return b}Ug.prototype.equals=function(a){if(I(a,this))return!0;if(!Xg(a))return!1;a=N(a,Xg,Ug);return U(this.g,a.g)};Ug.prototype.T=function(){return gf(this.g)};Ug.prototype.toString=function(){return this.g!=null?"Optional.of("+J(J(this.g))+")":"Optional.empty()"};function Yg(){Yg=k();Vg=Wg(null)}function Xg(a){return a instanceof Ug}Ug.prototype.A=["java.util.Optional",0];function mf(a,b){var c=a.size();b.length<c&&(b=Gf(Array(c),b));var d=b;a=a.G();for(var e=0;e<c;e=e+1|0)R(d,e,a.j());b.length>c&&R(b,c,null);return b};function Af(){this.v=this.o=0}t(Af,H);Af.prototype.g=function(){return this.o<this.C.size()};Af.prototype.j=function(){Lf(this.g());var a;return this.C.Y(this.v=(a=this.o,this.o=this.o+1|0,a))};Af.prototype.l=function(){Mf(this.v!=-1);this.C.Ia(this.v);this.o=this.v;this.v=-1};Af.prototype.A=["java.util.AbstractList$IteratorImpl",0];function Bf(){Af.call(this)}t(Bf,Af);Bf.prototype.A=["java.util.AbstractList$ListIteratorImpl",0];function Ag(){}t(Ag,yf);q=Ag.prototype;q.contains=function(a){return this.indexOf(a)!=-1};q.Y=function(a){var b=this.size();Ef(a,b);return this.g[a]};q.size=function(){return this.g.length};q.fa=function(){return this.la(Array(this.g.length))};q.G=function(){var a=new Zg;a.v=this.g;return a};q.la=function(a){var b=this.g.length;a.length<b&&(a=Gf(Array(b),a));for(var c=0;c<b;c=c+1|0)R(a,c,this.g[c]);a.length>b&&R(a,b,null);return a};q.A=["java.util.Arrays$ArrayList",0];function Zg(){this.o=0}t(Zg,H);Zg.prototype.g=function(){return this.o<this.v.length};Zg.prototype.j=function(){Lf(this.g());var a;return this.v[a=this.o,this.o=this.o+1|0,a]};Zg.prototype.l=function(){throw T().g;};Zg.prototype.A=["javaemul.internal.ArrayIterator",0];function $g(a,b){if(I(a,b))return!0;if(a==null||b==null||a.length!=b.length)return!1;for(var c=0;c<a.length;c=c+1|0)if(!U(a[c],b[c]))return!1;return!0}function ah(a){if(a==null)return 0;for(var b=1,c=0;c<a.length;c++)b=Math.imul(31,b)+gf(a[c])|0;return b};function bh(){}t(bh,Ce);bh.prototype.A=["java.lang.NumberFormatException",0];function Jf(a,b,c,d,e){var f=a.length,g=c.length;if(b<0||d<0||e<0||(b+e|0)>f||(d+e|0)>g)throw se().g;if(e!=0)if(I(a,c)&&b<d)for(b=b+e|0,e=d+e|0;e>d;)R(c,e=e-1|0,a[b=b-1|0]);else for(e=d+e|0;d<e;)g=f=void 0,R(c,(f=d,d=d+1|0,f),a[g=b,b=b+1|0,g])};function Gf(a,b){a.W=b.W;return a};function Ve(a){var b=new Be;fe(b,a);M(b,Error(b));throw b.g;}function Lf(a){if(!a)throw Me().g;}function Mf(a){if(!a)throw Fe().g;}function S(a){ch(a);return a}function ch(a){if(a==null)throw ke().g;return a}function Ef(a,b){if(a<0||a>=b)throw te("Index: "+a+", Size: "+b).g;}function Ye(a,b){if(a<0||a>=b)throw Ie("Index: "+a+", Size: "+b).g;}function Cf(a,b){if(a<0||a>b)throw te("Index: "+a+", Size: "+b).g;};function me(a,b,c){return dh(a,eh(b,c,a.length))}function dh(a,b){var c=a[0];if(c==null)return null;var d=new globalThis.Array(c);b&&(d.W=b);if(a.length>1){a=a.slice(1);b=b&&eh(b.ha,b.Ea,b.ga-1);for(var e=0;e<c;e++)d[e]=dh(a,b)}else if(b&&(a=b.ha.Db,a!==void 0))for(b=0;b<c;b++)d[b]=a;return d}function fh(a){a.W=eh(bf,rg,1);return a}
function R(a,b,c){var d;if(!(d=c==null))a:{if(d=a.W)if(d.ga>1){if(!Kf(c,d.ha,d.Ea,d.ga-1)){d=!1;break a}}else if(c!=null&&!d.Ea(c)){d=!1;break a}d=!0}if(!d)throw a=new Ae,ce(a),M(a,Error(a)),a.g;a[b]=c}function Kf(a,b,c,d){if(a==null||!Array.isArray(a))return!1;a=a.W||{ha:H,ga:1};var e=a.ga;return e==d?(d=a.ha,d===b?!0:b&&b.prototype.Xa||d&&d.prototype.Xa?!1:c(d.prototype)):e>d?H==b:!1}function eh(a,b,c){return{ha:a,Ea:b,ga:c}};function bf(){}t(bf,H);function J(a){return a==null?"null":a.toString()}function gh(a,b){Ye(b,a.length+1|0);return a.substr(b)}function hh(a,b,c){var d=a.length;if(b<0||c>d||c<b)throw Ie("fromIndex: "+b+", toIndex: "+c+", length: "+d).g;return a.substr(b,c-b|0)}function rg(a){return"string"===typeof a}bf.prototype.A=["java.lang.String",0];function ih(){}var jh,kh;t(ih,H);function lh(){lh=k();kh=new mh;jh=new nh}ih.prototype.A=["java.util.Locale",0];function mh(){}t(mh,ih);mh.prototype.toString=ba("");mh.prototype.A=["java.util.Locale$1",0];function nh(){}t(nh,ih);nh.prototype.toString=ba("unknown");nh.prototype.A=["java.util.Locale$4",0];function oh(a,b){this.g=a;this.j=b}t(oh,H);function K(a,b){var c=b||0;return We(a,"$$class/"+c,function(){return new oh(a,c)})}function Zd(a){return a.j!=0?J(ph("[",a.j))+J(a.g.prototype.A[1]==3?a.g.prototype.A[2]:"L"+J(a.g.prototype.A[0])+";"):a.g.prototype.A[0]}function qh(a){return J(a.g.prototype.A[0])+J(ph("[]",a.j))}function rh(a,b){return gh(a,a.lastIndexOf(b)+1|0)}
oh.prototype.toString=function(){return String(this.j==0&&this.g.prototype.A[1]==1?"interface ":this.j==0&&this.g.prototype.A[1]==3?"":"class ")+J(Zd(this))};function ph(a,b){for(var c="",d=0;d<b;d=d+1|0)c=J(c)+J(a);return c}oh.prototype.A=["java.lang.Class",0];function ne(){this.g=0}t(ne,H);ne.prototype.equals=function(a){return oe(a)?this.g==a.g&&I(this.o,a.o)&&I(this.l,a.l)&&I(this.j,a.j):!1};ne.prototype.T=function(){var a=[jf(this.g),this.l,this.o,this.j];return ah(a)};ne.prototype.toString=function(){return J(this.l)+"."+J(this.o)+"("+J(this.j!=null?this.j:"Unknown Source")+String(this.g>=0?":"+this.g:"")+")"};function oe(a){return a instanceof ne}ne.prototype.A=["java.lang.StackTraceElement",0];function ie(){}function he(a){return a instanceof Error}ie.prototype.A=["Error",0];function ge(a,b){if(a instanceof Object)try{a.hb=b,Object.defineProperties(a,{cause:{get:function(){return b.o&&b.o.g}}})}catch(c){}};function uh(a,b){de(this);this.o=b;this.j=a;ee(this);M(this,Error(this))}t(uh,P);uh.prototype.getMessage=p("j");fa.Object.defineProperties(uh.prototype,{error:{configurable:!0,enumerable:!0,get:function(){var a=Error(),b=this.g;a.fileName=b.fileName;a.lineNumber=b.lineNumber;a.columnNumber=b.columnNumber;a.message=b.message;a.name=b.name;a.stack=b.stack;a.toSource=b.toSource;a.cause=b.cause;for(var c in b)c.indexOf("__java$")!=0&&(a[c]=b[c]);return a}}});
uh.prototype.A=["com.google.apps.docs.xplat.base.XplatException",0];function vh(){}function wh(a){return a instanceof Error}vh.prototype.A=["Error",0];function xh(){var a=a==null?function(c){return Se(Math.floor(Math.random()*c))}:a;var b=(a(2147483647)>>>0).toString(16);b=J(yh("0",Math.max(0,8-b.length|0)))+J(b);a=(a(2147483647)>>>0).toString(16);return J(a)+J(b)};function zh(){}function Ah(a){return a instanceof Array}zh.prototype.A=["Array",0];function Bh(){}function Ch(a){return a instanceof Object}Bh.prototype.A=["Object",0];var yh=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};function Dh(){}function X(a){return new RegExp(a,"")}function Eh(a){return a instanceof RegExp}Dh.prototype.A=["RegExp",0];function Fh(){}t(Fh,H);function Gh(a,b){var c=new Fh;if(b==null)throw ke().g;c.j=N(b,rg,bf);b="g";a.multiline&&(b=J(b)+"m");a.ignoreCase&&(b=J(b)+"i");c.l=new RegExp(a.source,b);return c}function Hh(a){a.g=a.l.exec(a.j);return a.g!=null}Fh.prototype.A=["com.google.apps.xplat.regex.RegExpMatcher",0];function Ih(){}function Jh(a){return a instanceof Object}Ih.prototype.A=["Object",0];var Kh={Zb:"build-label",Eb:"buildLabel",Fb:"clientLog",Jb:"docId",bc:"mobile-app-version",mc:"severity",sc:"severity-unprefixed",Ub:"isArrayPrototypeIntact",Vb:"isEditorElementAttached",Ob:"documentCharacterSet",Xb:"isModuleLoadFailure",kc:"reportName",ac:"locale",Hb:"createdOnServer",fc:"numUnsavedCommands",Ib:"cspViolationContext",jc:"relatedToBrowserExtension",tc:"workerError",Kb:"docosPostLimitExceeded",Lb:"docosPostLimitType",Mb:"docosReactionLimitExceeded",Nb:"docosReactionLimitType",lc:"saveTakingTooLongOnClient",
oc:"truncatedCommentNotificationsCount",qc:"truncatedCommentNotificationsFromPayload",ec:"nonfatalReason"};function Lh(){this.g=!1}t(Lh,H);q=Lh.prototype;q.dispose=function(){this.g||(this.g=!0,this.ya(),rh(rh(qh(K($d(this))),"."),"$"))};q.wa=p("g");q.ya=function(){if(this.o!=null){for(var a=this.o,b=0;b<a.length;b++)a[b].dispose();this.o.length=0}};q.toString=function(){return H.prototype.toString.call(this)||""};q.A=["com.google.apps.xplat.disposable.Disposable",0];/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Mh=globalThis.trustedTypes,Nh;function Oh(){var a=null;if(!Mh)return a;try{var b=aa();a=Mh.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};function Ph(a){this.g=a}Ph.prototype.toString=function(){return this.g+""};var Qh=pa([""]),Rh=qa(["\x00"],["\\0"]),Sh=qa(["\n"],["\\n"]),Th=qa(["\x00"],["\\u0000"]);function Uh(a){return a.toString().indexOf("`")===-1}Uh(function(a){return a(Qh)})||Uh(function(a){return a(Rh)})||Uh(function(a){return a(Sh)})||Uh(function(a){return a(Th)});function Vh(a,b){if(b instanceof Ph)b=b.g;else throw Error("");a.src=b.toString()};function Wh(a){var b=y.onerror;y.onerror=function(c,d,e,f,g){b&&b(c,d,e,f,g);a({message:c,fileName:d,line:e,lineNumber:e,vc:f,error:g});return!0}}
function Xh(a){var b=za("window.location.href");a==null&&(a='Unknown Error of type "null/undefined"');if(typeof a==="string")return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:b,stack:"Not available"};var c=!1;try{var d=a.lineNumber||a.line||"Not available"}catch(f){d="Not available",c=!0}try{var e=a.fileName||a.filename||a.sourceURL||y.$googDebugFname||b}catch(f){e="Not available",c=!0}b=Yh(a);return!c&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?{message:a.message,
name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:b}:(c=a.message,c==null&&(c=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:Zh(a.constructor))+'"':"Unknown Error of unknown type",typeof a.toString==="function"&&Object.prototype.toString!==a.toString&&(c+=": "+a.toString())),{message:c,name:a.name||"UnknownError",lineNumber:d,fileName:e,stack:b||"Not available"})}
function Yh(a,b){b||(b={});b[$h(a)]=!0;var c=a.stack||"",d=a.cause;d&&!b[$h(d)]&&(c+="\nCaused by: ",d.stack&&d.stack.indexOf(d.toString())==0||(c+=typeof d==="string"?d:d.message+"\n"),c+=Yh(d,b));a=a.errors;if(Array.isArray(a)){d=1;var e;for(e=0;e<a.length&&!(d>4);e++)b[$h(a[e])]||(c+="\nInner error "+d++ +": ",a[e].stack&&a[e].stack.indexOf(a[e].toString())==0||(c+=typeof a[e]==="string"?a[e]:a[e].message+"\n"),c+=Yh(a[e],b));e<a.length&&(c+="\n... "+(a.length-e)+" more inner errors")}return c}
function $h(a){var b="";typeof a.toString==="function"&&(b=""+a);return b+a.stack}function ai(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,ai));a.stack||(a.stack=bi(ai));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function ci(a,b){a=ai(a);if(b)for(var c in b)nb(a,c,b[c]);return a}
function bi(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||bi),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=di(a||arguments.callee.caller,[]));return b}
function di(a,b){var c=[];if(Array.prototype.indexOf.call(b,a,void 0)>=0)c.push("[...circular reference...]");else if(a&&b.length<50){c.push(Zh(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=Zh(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");
try{c.push(di(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?c.push("[...long stack...]"):c.push("[end]");return c.join("")}function Zh(a){if(ei[a])return ei[a];a=String(a);if(!ei[a]){var b=/function\s+([^\(]+)/m.exec(a);ei[a]=b?b[1]:"[Anonymous]"}return ei[a]}var ei={};function fi(a,b){this.name=a;this.value=b}fi.prototype.toString=p("name");var gi=new fi("SEVERE",1E3),hi=new fi("WARNING",900),ii=new fi("CONFIG",700);function ji(){this.clear()}var ki;function li(a){var b=mi(),c=b.g;if(c[0]){var d=b.j;b=b.l?d:-1;do b=(b+1)%0,a(c[b]);while(b!==d)}}ji.prototype.clear=function(){this.g=[];this.j=-1;this.l=!1};function mi(){ki||(ki=new ji);return ki};var ni=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function oi(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function pi(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]}function qi(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)qi(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))}function ri(a,b){var c=[];for(b=b||0;b<a.length;b+=2)qi(a[b],a[b+1],c);return c.join("&")}
function si(a){var b=[],c;for(c in a)qi(c,a[c],b);return b.join("&")}function ti(a,b){var c=arguments.length==2?ri(arguments[1],0):ri(arguments,1);return pi(a,c)};function ui(a){a&&typeof a.dispose=="function"&&a.dispose()};function vi(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Ba(d)?vi.apply(null,d):ui(d)}};function Y(){this.H=this.H;this.C=this.C}Y.prototype.H=!1;Y.prototype.wa=p("H");Y.prototype.dispose=function(){this.H||(this.H=!0,this.M())};Y.prototype[Symbol.dispose]=function(){this.dispose()};function wi(a,b){b=Ia(ui,b);a.H?b():(a.C||(a.C=[]),a.C.push(b))}Y.prototype.M=function(){if(this.C)for(;this.C.length;)this.C.shift()()};var xi=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:aa();function yi(a,b){this.l=a;this.o=b;this.j=0;this.g=null}yi.prototype.get=function(){if(this.j>0){this.j--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};function zi(a,b){a.o(b);a.j<100&&(a.j++,b.next=a.g,a.g=b)};var Ai=[],Bi=[],Ci=!1;function Di(a){Ai[Ai.length]=a;if(Ci)for(var b=0;b<Bi.length;b++)a(Ha(Bi[b].g,Bi[b]))};Di(k());function Ei(){this.j=this.g=null}Ei.prototype.add=function(a,b){var c=Fi.get();c.set(a,b);this.j?this.j.next=c:this.g=c;this.j=c};Ei.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.j=null),a.next=null);return a};var Fi=new yi(function(){return new Gi},function(a){return a.reset()});function Gi(){this.next=this.scope=this.g=null}Gi.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};Gi.prototype.reset=function(){this.next=this.scope=this.g=null};var Hi,Ii=!1,Ji=new Ei;function Ki(a,b){Hi||Li();Ii||(Hi(),Ii=!0);Ji.add(a,b)}function Li(){var a=Promise.resolve(void 0);Hi=function(){a.then(Mi)}}function Mi(){for(var a;a=Ji.remove();){try{a.g.call(a.scope)}catch(b){Ma(b)}zi(Fi,a)}Ii=!1};function Ni(){};function Oi(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function Pi(a){this.g=0;this.H=void 0;this.o=this.j=this.l=null;this.v=this.C=!1;if(a!=Ni)try{var b=this;a.call(void 0,function(c){Qi(b,2,c)},function(c){Qi(b,3,c)})}catch(c){Qi(this,3,c)}}function Ri(){this.next=this.l=this.j=this.o=this.g=null;this.v=!1}Ri.prototype.reset=function(){this.l=this.j=this.o=this.g=null;this.v=!1};var Si=new yi(function(){return new Ri},function(a){a.reset()});function Ti(a,b,c){var d=Si.get();d.o=a;d.j=b;d.l=c;return d}
function Ui(){var a=new Pi(Ni);Qi(a,2);return a}function Vi(a,b,c){Wi(a,b,c,null)||Ki(Ia(b,a))}function Xi(a){return new Pi(function(b,c){a.length||b(void 0);for(var d,e=0;e<a.length;e++)d=a[e],Vi(d,b,c)})}function Yi(a){return new Pi(function(b){var c=a.length,d=[];if(c)for(var e=function(h,l,m){c--;d[h]=l?{rb:!0,value:m}:{rb:!1,reason:m};c==0&&b(d)},f,g=0;g<a.length;g++)f=a[g],Vi(f,Ia(e,g,!0),Ia(e,g,!1));else b(d)})}function Zi(){var a,b,c=new Pi(function(d,e){a=d;b=e});return new $i(c,a,b)}
Pi.prototype.then=function(a,b,c){return aj(this,xi(typeof a==="function"?a:null),xi(typeof b==="function"?b:null),c)};Pi.prototype.$goog_Thenable=!0;q=Pi.prototype;q.za=function(a,b){return aj(this,null,xi(a),b)};q.Ya=Pi.prototype.za;q.cancel=function(a){if(this.g==0){var b=new bj(a);Ki(function(){cj(this,b)},this)}};
function cj(a,b){if(a.g==0)if(a.l){var c=a.l;if(c.j){for(var d=0,e=null,f=null,g=c.j;g&&(g.v||(d++,g.g==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.g==0&&d==1?cj(c,b):(f?(d=f,d.next==c.o&&(c.o=d),d.next=d.next.next):dj(c),ej(c,e,3,b)))}a.l=null}else Qi(a,3,b)}function fj(a,b){a.j||a.g!=2&&a.g!=3||gj(a);a.o?a.o.next=b:a.j=b;a.o=b}
function aj(a,b,c,d){var e=Ti(null,null,null);e.g=new Pi(function(f,g){e.o=b?function(h){try{var l=b.call(d,h);f(l)}catch(m){g(m)}}:f;e.j=c?function(h){try{var l=c.call(d,h);l===void 0&&h instanceof bj?g(h):f(l)}catch(m){g(m)}}:g});e.g.l=a;fj(a,e);return e.g}q.Bb=function(a){this.g=0;Qi(this,2,a)};q.Cb=function(a){this.g=0;Qi(this,3,a)};
function Qi(a,b,c){a.g==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,Wi(c,a.Bb,a.Cb,a)||(a.H=c,a.g=b,a.l=null,gj(a),b!=3||c instanceof bj||hj(a,c)))}function Wi(a,b,c,d){if(a instanceof Pi)return fj(a,Ti(b||Ni,c||null,d)),!0;if(Oi(a))return a.then(b,c,d),!0;if(Ca(a))try{var e=a.then;if(typeof e==="function")return ij(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1}
function ij(a,b,c,d,e){function f(l){h||(h=!0,d.call(e,l))}function g(l){h||(h=!0,c.call(e,l))}var h=!1;try{b.call(a,g,f)}catch(l){f(l)}}function gj(a){a.C||(a.C=!0,Ki(a.qb,a))}function dj(a){var b=null;a.j&&(b=a.j,a.j=b.next,b.next=null);a.j||(a.o=null);return b}q.qb=function(){for(var a;a=dj(this);)ej(this,a,this.g,this.H);this.C=!1};
function ej(a,b,c,d){if(c==3&&b.j&&!b.v)for(;a&&a.v;a=a.l)a.v=!1;if(b.g)b.g.l=null,jj(b,c,d);else try{b.v?b.o.call(b.l):jj(b,c,d)}catch(e){kj.call(null,e)}zi(Si,b)}function jj(a,b,c){b==2?a.o.call(a.l,c):a.j&&a.j.call(a.l,c)}function hj(a,b){a.v=!0;Ki(function(){a.v&&kj.call(null,b)})}var kj=Ma;function bj(a){La.call(this,a);this.g=!1}z(bj,La);bj.prototype.name="cancel";function $i(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function lj(){this.v=[];this.o=this.l=!1;this.j=void 0;this.F=this.L=this.H=!1;this.C=0;this.g=null;this.B=0}lj.prototype.cancel=function(a){if(this.l)this.j instanceof lj&&this.j.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.B--,b.B<=0&&b.cancel())}this.F=!0;this.l||(a=new mj(this),nj(this),oj(this,!1,a))}};lj.prototype.J=function(a,b){this.H=!1;oj(this,a,b)};function oj(a,b,c){a.l=!0;a.j=c;a.o=!b;pj(a)}function nj(a){if(a.l){if(!a.F)throw new qj(a);a.F=!1}}
function rj(a){throw a;}function sj(a,b,c){return tj(a,b,null,c)}function uj(a,b,c){tj(a,b,function(d){var e=b.call(this,d);if(e===void 0)throw d;return e},c)}function tj(a,b,c,d){var e=a.l;e||(b===c?b=c=xi(b):(b=xi(b),c=xi(c)));a.v.push([b,c,d]);e&&pj(a);return a}lj.prototype.then=function(a,b,c){var d,e,f=new Pi(function(g,h){e=g;d=h});tj(this,e,function(g){g instanceof mj?f.cancel():d(g);return vj},this);return f.then(a,b,c)};lj.prototype.$goog_Thenable=!0;
function wj(a){return bb(a.v,function(b){return typeof b[1]==="function"})}var vj={};
function pj(a){if(a.C&&a.l&&wj(a)){var b=a.C,c=xj[b];c&&(y.clearTimeout(c.g),delete xj[b]);a.C=0}a.g&&(a.g.B--,delete a.g);b=a.j;for(var d=c=!1;a.v.length&&!a.H;){var e=a.v.shift(),f=e[0],g=e[1];e=e[2];if(f=a.o?g:f)try{var h=f.call(e||null,b);h===vj&&(h=void 0);h!==void 0&&(a.o=a.o&&(h==b||h instanceof Error),a.j=b=h);if(Oi(b)||typeof y.Promise==="function"&&b instanceof y.Promise)d=!0,a.H=!0}catch(l){b=l,a.o=!0,wj(a)||(c=!0)}}a.j=b;d&&(h=Ha(a.J,a,!0),d=Ha(a.J,a,!1),b instanceof lj?(tj(b,h,d),b.L=
!0):b.then(h,d));c&&(b=new yj(b),xj[b.g]=b,a.C=b.g)}function zj(a){var b=new lj;nj(b);oj(b,!0,a);return b}function qj(){La.call(this)}z(qj,La);qj.prototype.message="Deferred has already fired";qj.prototype.name="AlreadyCalledError";function mj(){La.call(this)}z(mj,La);mj.prototype.message="Deferred was canceled";mj.prototype.name="CanceledError";function yj(a){this.g=y.setTimeout(Ha(this.l,this),0);this.j=a}yj.prototype.l=function(){delete xj[this.g];rj(this.j)};var xj={};function Aj(){}function Bj(a){return a!=null&&!!a.Ja}Aj.prototype.Ja=!0;Aj.prototype.A=["com.google.apps.docs.xplat.flag.FlagService",1];var Cj;function Dj(){if(Cj==null){var a=new Ej(null);Cj=function(){return a}}var b;return N((b=Cj,b()),Bj,Aj)};function Fj(){}t(Fj,H);Fj.prototype.get=function(){if(this.j==null){var a=N(y._docs_flag_initialData,Ch,Bh);this.j=a!=null?a:N({},Ch,Bh)}return this.j};Fj.prototype.g=function(){return this.get()};Fj.prototype.A=["com.google.apps.docs.xplat.flag.FlagServiceHelper",0];function Gj(a){return typeof a=="string"?a=="true"||a=="1":!!a};function Ej(a){this.g=new Fj;if(a!=null)for(var b in a){var c=b,d=a[b],e=N(this.g.g(),Ch,Bh);xe(d)?(d=N(d,xe,we).ca,e[c]=d):e[c]=d!=null?d:null}}t(Ej,H);Ej.prototype.clear=function(){this.g=new Fj};Ej.prototype.get=function(a){return N(this.g.g(),Ch,Bh)[a]};function Hj(a,b){a=N(a.g.g(),Ch,Bh);return b in a}
function Ij(a,b){if(!Hj(a,b)||a.get(b)==null)return NaN;try{var c=J(a.get(b));Oe==null&&(Oe=RegExp("^\\s*[+-]?(NaN|Infinity|((\\d+\\.?\\d*)|(\\.\\d+))([eE][+-]?\\d+)?[dDfF]?)\\s*$"));if(!Oe.test(c)){var d=new bh;fe(d,'For input string: "'+J(c)+'"');M(d,Error(d));throw d.g;}return parseFloat(c)}catch(f){var e=je(f);if(e instanceof bh)return NaN;throw e.g;}}
function Jj(a,b){if(!Hj(a,b))return"";a=a.get(b);if(a==null)var c="";else{if(b="number"===typeof a){b=Qe(S(a));var d=Qe(S(a));b=b.equals(d)}b?c=""+Qe(S(a)):c=J(a)}return c}Ej.prototype.Ja=!0;Ej.prototype.A=["com.google.apps.docs.xplat.flag.FlagServiceImpl",0];function Kj(a){uh.call(this,a,null);M(this,Error(this))}t(Kj,uh);Kj.prototype.A=["com.google.apps.docs.xplat.net.LimitException",0];function Lj(a,b,c,d){this.g=!1;this.v=a;this.l=b;this.j=new Mj(Math.imul(c,1E3),d)}t(Lj,Lh);Lj.prototype.A=["com.google.apps.docs.xplat.net.QpsLimiter",0];function Nj(){this.l=this.o=this.g=0}t(Nj,H);function Oj(a){return a instanceof Nj}Nj.prototype.A=["com.google.apps.docs.xplat.util.BasicStat$Slot",0];function Mj(a){this.j=0;this.l=a;this.j=Re(a/50);this.g=new Pj(jf(50))}t(Mj,H);Mj.prototype.get=function(a){return Qj(this,a,function(b,c){b=N(b,xe,we);c=N(c,Oj,Nj);return jf(b.ca+c.g|0)})};function Qj(a,b,c){b=b!=null?S(b):Id(Pd(Date.now()));Rj(a,b);var d=0;b=Sj(a,S(b));b=S(b)-a.l;for(var e=a.g.g.length-1|0;e>=0;e=e-1|0){var f=N(a.g.get(e),Oj,Nj);if(S(f.j)<=b)break;d=N(c(jf(d),f),xe,we).ca}return d}function Sj(a,b){return a.j*Math.floor(b/a.j+1)}
function Rj(a,b){var c=N(Tj(a.g),Oj,Nj);c!=null&&(c=S(c.j)-a.j,S(b)<S(c)&&a.g.clear())}Mj.prototype.A=["com.google.apps.docs.xplat.util.BasicStat",0];function Pj(a){this.j=this.l=0;a!=null?"number"===typeof a?(a=S(a),a=Se(a)):a=a instanceof Hd?S(a).K:a.ca:a=100;this.l=a;this.g=N([],Ah,zh)}t(Pj,H);q=Pj.prototype;q.add=function(a){var b=this.g[this.j];this.g[this.j]=a;this.j=Re((this.j+1|0)%this.l);return b};q.get=function(a){a=Uj(this,a);return this.g[a]};q.set=function(a,b){a=Uj(this,a);this.g[a]=b};q.clear=function(){this.j=this.g.length=0};
q.ta=function(){for(var a=this.g.length,b=this.g.length-this.g.length|0,c=N([],Ah,zh);b<a;b=b+1|0){var d=c,e=this.get(b);d.push(e)}return c};function Tj(a){return a.g.length==0?null:a.get(a.g.length-1|0)}function Uj(a,b){if(b>=a.g.length)throw se().g;return a.g.length<a.l?b:Re((a.j+b|0)%a.l)}q.A=["com.google.apps.docs.xplat.util.CircularBuffer",0];function Vj(){this.g=0}var Wj,Xj;t(Vj,H);function Z(a,b){var c=new Vj;c.j=a;c.g=b;Wj[a]=c!==void 0?c:null;return c}Vj.prototype.toString=p("j");
function Yj(){Yj=k();Wj=N({},Jh,Ih);Z("IDLE",1);Z("BUSY",1);Z("RECOVERING",2);Xj=Z("OFFLINE",3);Z("SERVER_DOWN",3);Z("FORBIDDEN",4);Z("AUTH_REQUIRED",4);Z("SESSION_LIMIT_EXCEEDED",5);Z("LOCKED",5);Z("INCOMPATIBLE_SERVER",5);Z("CLIENT_ERROR",5);Z("CLIENT_FATAL_ERROR",5);Z("CLIENT_FATAL_ERROR_PENDING_CHANGES",5);Z("BATCH_CLIENT_ERROR",3);Z("SAVE_ERROR",5);Z("DOCUMENT_TOO_LARGE",5);Z("BATCH_SAVE_ERROR",3);Z("DOCS_EVERYWHERE_IMPORT_ERROR",5);Z("POST_LIMIT_EXCEEDED_ERROR",5);Z("DOCS_QUOTA_EXCEEDED_ERROR",
5)}Vj.prototype.A=["com.google.apps.docs.xplat.net.Status$State",0];function Zj(){}t(Zj,H);function ak(a){return a instanceof Zj}Zj.prototype.A=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function bk(){this.g=!1;this.j=N([],Ah,zh)}t(bk,Lh);function ck(a,b,c){var d;a:{for(d=0;d<a.j.length;d=d+1|0){var e=N(a.j[d],ak,Zj);if(I(e.j,c)&&I(e.g,b)){d=!0;break a}}d=!1}d||(a=a.j,c=b.g(c),d=new Zj,d.g=b,d.j=c,a.push(d))}bk.prototype.ya=function(){this.removeAll();Lh.prototype.ya.call(this)};bk.prototype.removeAll=function(){for(var a=N(this.j.pop(),ak,Zj);a!=null;)a.g.j(a.j),a=N(this.j.pop(),ak,Zj)};bk.prototype.A=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];var dk,ek,fk,gk,hk,ik,jk,kk,lk,mk,nk,ok,pk,qk,rk,sk;
function tk(){tk=k();dk=sf();ek=sf();fk=vf(fh("Trusted Type;TrustedHTML;TrustedScript;cannot communicate with background;zaloJSV2;kaspersky-labs;@user-script;Object Not Found Matching Id;contextChanged;Not implemented on this platform;Extension context invalidated;neurosurgeonundergo;realTimeClData;Failed to execute 'querySelectorAll' on 'Document';Promise.all(...).then(...).catch(...).finally is not a function;Error executing Chrome API, chrome.tabs;zotero;enableLTSeparator;Identifier 'originalPrompt' has already been declared;User rejected the request;Could not inject ethereum provider because it's not your default extension;Cannot redefine property: googletag;Can't find variable: HTMLDialogElement;Identifier 'listenerName' has already been declared;Cannot read properties of undefined (reading 'info');Permission denied to access property \"type\";Error: Promise timed out;Request timeout ToolbarStatus;Can't find variable: nc;imtgo;ton is not a function;__renderMessageNode is not defined;Cannot redefine property: ethereum".split(";")));gk=
vf(fh("puppeteer-core;kaspersky-labs;@user-script;jsQuilting;linkbolic;neurosurgeonundergo;tlscdn;https://cdnjs.cloudflare.com/ajax/libs/mathjax/;secured-pixel.com;Can't find variable: nc;imtgo;_simulateEvent".split(";")));hk=sf(X("^_0x[a-f0-9]{6} is not defined$"));ik=vf(fh("egfdjlfmgnehecnclamagfafdccgfndp mndnfokpggljbaajbnioimlmbfngpief mlkejohendkgipaomdopolhpbihbhfnf kgonammgkackdilhodbgbmodpepjocdp klbcgckkldhdhonijdbnhhaiedfkllef pmehocpgjmkenlokgjfkaichfjdhpeol cjlaeehoipngghikfjogbdkpbdgebppb ghbmnnjooekpmoecnnnilnnbdlolhkhi lmjegmlicamnimmfhcmpkclmigmmcbeh gmbmikajjgmnabiglmofipeabaddhgne lpcaedmchfhocbbapmcbpinfpgnhiddi gbkeegbaiigmenfmjfclcdgdpimamgkj adokjfanaflbkibffcbhihgihpgijcei".split(" ")));
jk=sf(X("chrome-extension://([^\\/]+)"),X("moz-extension://([^\\/]+)"),X("ms-browser-extension://([^\\/]+)"),X("webkit-masked-url://([^\\/]+)"),X("safari-web-extension://([^\\/]+)"));kk=vf(fh('status is 0, navigator.onLine =;Network sync is disabled. Aborting a network request of int type;The service is currently unavailable.;Internal error encountered.;A network error occurred and the request could not be completed.;data does not exist in AF cache;There was an error during the transport or processing of this request;Failed to retrieve dependencies of service;Failed to load gapi;Rpc failed due to xhr error. error code: 6, error:  [0];An interceptor has requested that the request be retried;8,"generic";A network error occurred'.split(";")));
lk=sf(X("^Permission denied$"));mk=vf(fh("Kg is not defined;uncaught error;The play method is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.;Illegal invocation;Script error;zCommon;can't access dead object;Java exception was raised during method invocation;pauseVideo is not a function;ResizeObserver loop;wallet must has at least one account;xbrowser is not defined;jQuery is not defined;Cannot read properties of null (reading 'requestAnimationFrame');Class extends value undefined is not a constructor or null;GM3TooltipService: No tooltip with id;Mole was disposed;getInitialTopicListResponse is missing for stream rendering;getPeopleById call preempted;The operation is insecure;class heritage;The play() request was interrupted;args.site.enabledFeatures is undefined".split(";")));
nk=sf(X("phantomjs|node:electron|py-scrap|eval code|Program Files"));ok=sf(X("Script https:\\/\\/meet.google.com\\/.*meetsw.*load failed"),X("A bad HTTP response code \\(\\d+\\) was received when fetching the script"));pk=vf(fh("Service worker registration is disabled by MDA;An unknown error occurred when fetching the script;Operation has been aborted;Timed out while trying to start the Service Worker;The Service Worker system has shutdown;The user denied permission to use Service Worker;The script resource is behind a redirect, which is disallowed;The document is in an invalid state;ServiceWorker script evaluation failed;ServiceWorker cannot be started;Failed to access storage;Worker disallowed;encountered an error during installation".split(";")));
qk=sf(X("Error loading.*Consecutive load failures"),X("Failed to load module.*Consecutive load failures"));rk=sf(X("Error loading.*Consecutive load failures"),X("Failed to load module.*Consecutive load failures"));sk=sf("Timeout reached for loading script https://www.gstatic.com/_/apps-fileview/_/js/","Error while loading script https://www.gstatic.com/_/apps-fileview/_/js/")};function uk(){}t(uk,H);function vk(a){return a instanceof uk}uk.prototype.A=["com.google.apps.telemetry.xplat.error.ErrorClassifier",0];function rf(){}t(rf,uk);rf.prototype.v=function(a){a:{a=wk(a);for(var b=!1,c=(tk(),jk).G();c.g();){var d=N(c.j(),Eh,Dh);for(d=Gh(d,a);Hh(d);){b=d;if(b.g==null)throw a=new Ee,fe(a,"No match available"),M(a,Error(a)),a.g;if(1>(b.g.length-1|0))throw te("No group 1").g;b=N(b.g[1],rg,bf);Yg();b=b==null?Vg:Wg(ch(b));b=N(b.g!=null?b.g:"",rg,bf);if(ik.contains(b)){a=!1;break a}b=!0}}a=b}return a};rf.prototype.A=["com.google.apps.telemetry.xplat.error.BaseExtensionErrorClassifier",0];function xk(){}t(xk,H);xk.prototype.equals=function(a){return yk(this,a)};xk.prototype.T=function(){for(var a=1,b=zk(this),c=0;c<b.length;c++){var d=this[b[c]];d!=null&&(d=d.W?ah(d):af(d),a=Math.imul(1000003,a)^d)}return a};xk.prototype.toString=function(){var a=Ue(this);a=rh(rh(qh(a),"."),"$");a=gh(a,a.lastIndexOf("AutoValue_")+1|0);a=nf(J(a)+"{","}");for(var b=zk(this),c=0;c<b.length;c++){var d=b[c],e=this[d];Array.isArray(e)&&(e="["+J(e)+"]");of(a,J(d)+"="+J(e))}return a.toString()};
function yk(a,b){if(b==null||!I(Ue(b),Ue(a)))return!1;var c=zk(a);if(c.length!=zk(b).length)return!1;for(var d=0;d<c.length;d++){var e=c[d],f=a[e];e=b[e];if(!(I(f,e)||(f==null||e==null?0:f.W&&e.W?I(Ue(f),Ue(e))&&$g(f,e):$e(f,e))))return!1}return!0}xk.prototype.A=["javaemul.internal.ValueType",0];function zk(a){var b=Object.keys(a),c=a.v;return c?b.filter(function(d){return!c.includes(d)}):b};function Ak(){}t(Ak,xk);Ak.prototype.A=["com.google.apps.telemetry.xplat.error.ErrorClassification",0];function Bk(){}t(Bk,H);function Ck(a,b){hf(b);a.l=b;return a}function Dk(a,b){hf(b);a.o=b;return a}function Ek(a){if(a.l==null||a.g==null||a.o==null)throw Fe().g;var b=new Fk,c=a.g,d=a.j,e=a.o;b.l=a.l;b.g=c;b.j=d;b.o=e;return b}Bk.prototype.A=["com.google.apps.telemetry.xplat.error.JsError$Builder",0];function Fk(){}t(Fk,xk);function Gk(a){var b="";a=a.j;a!=null&&(b=J(b)+(J(a.getMessage())+"\n"),b=J(b)+(J(a.g)+"\n"),b=J(b)+J(Gk(a)));return b}function wk(a){return J(a.getMessage())+"\n"+J(a.g)+"\n"+J(Gk(a))}Fk.prototype.getMessage=p("l");Fk.prototype.A=["com.google.apps.telemetry.xplat.error.JsError",0];function Hk(){this.g=!1}var Ik,Jk,Kk,Lk,Mk,Nk,Ok,Pk,Qk,tf,uf;t(Hk,H);
function Rk(a,b,c){if(b==null)return"no throwable";if(c>3)return"max depth reached";var d="",e=b.j!=null?b.j:"no message";if(b.l==null){a:{var f=b.g;if(he(f)&&(f=N(f,he,ie),f.stack!=null)){var g=f.stack,h=RegExp("\n","g");f=me([0],bf,rg);for(var l=0,m=g,n=null;;){var u=h.exec(m);if(u==null||m===""){R(f,l,m);break}else{var x=u.index;R(f,l,hh(m,0,x));m=hh(m,x+u.at(0).length|0,m.length);h.lastIndex=0;I(n,m)&&(R(f,l,hh(m,0,1)),m=gh(m,1));n=m;l=l+1|0}}if(g.length>0){for(g=f.length;g>0&&f[g-1|0]==="";)g=
g-1|0;g<f.length&&(f.length=g)}g=me([f.length],ne,oe);for(h=0;h<f.length;h=h+1|0)l=h,n=m=new ne,u=f[h],n.l="",n.o=u,n.j=null,n.g=-1,R(g,l,m);f=g;break a}f=me([0],ne,oe)}b.l=f}f=b.l;d=J(d)+(J(e)+"\ntop stack frame: "+J(f!=null&&f.length>0?f[0].toString():"no stack trace"));b=b.o;b!=null&&(d=J(d)+("\ncause: "+J(Rk(a,b,c+1|0))));return d}
function Sk(){Sk=k();tf=Tk((tk(),fk),gk,1);uf=Uk(hk,ek,1);Jk=Tk(kk,dk,2);Lk=Tk(mk,dk,3);Kk=Uk(lk,nk,3);Ok=Uk(qk,rk,4);Pk=Tk(sk,dk,4);Mk=Uk(ok,ek,5);Nk=Tk(pk,dk,5);Ik=qf();Qk=Uf("SEVERE","SEVERE_AFTER_INITIAL","FATAL","UNKNOWN","")}Hk.prototype.A=["com.google.apps.telemetry.xplat.error.ErrorProcessor",0];function Vk(){}t(Vk,xk);Vk.prototype.A=["com.google.apps.telemetry.xplat.error.ErrorProcessorResult",0];function Wk(){}t(Wk,uk);function Uk(a,b,c){var d=new Wk;d.j=c;d.g=0;d.l=a;d.o=b;return d}Wk.prototype.v=function(a){var b=Gk(a);return Xk(a.getMessage(),this.l)||Xk(a.g,this.o)||Xk(b,this.l)||Xk(b,this.o)};function Xk(a,b){for(b=b.G();b.g();){var c=N(b.j(),Eh,Dh);if(Hh(Gh(c,a)))return!0}return!1}Wk.prototype.A=["com.google.apps.telemetry.xplat.error.RegexErrorClassifier",0];function Yk(){this.o=!1}t(Yk,uk);Yk.prototype.v=function(a){if(this.o)a:{a=a.getMessage();for(var b=0;b<this.l.size();b=b+1|0){var c=a,d=N(this.l.Y(b),rg,bf);if(I(S(c),d)){a=!0;break a}}a=!1}else a=wk(a),a=Zk(a,this.l)||Zk(a,this.C);return a};function Zk(a,b){for(var c=0;c<b.size();c=c+1|0){var d=a,e=N(b.Y(c),rg,bf);if(d.indexOf(e.toString())!=-1)return!0}return!1}function Tk(a,b,c){var d=new Yk;d.j=c;d.g=0;d.l=a;d.C=b;d.o=!1;return d}
Yk.prototype.A=["com.google.apps.telemetry.xplat.error.StringErrorClassifier",0];function $k(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function al(a){var b={},c;for(c in a)b[c]=a[c];return b}var bl="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function cl(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<bl.length;f++)c=bl[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function dl(a){this.g=this.H=this.o="";this.B=null;this.C=this.j="";this.v=!1;var b;a instanceof dl?(this.v=a.v,el(this,a.o),this.H=a.H,this.g=a.g,fl(this,a.B),gl(this,a.j),hl(this,a.l.clone()),this.C=a.C):a&&(b=String(a).match(ni))?(this.v=!1,el(this,b[1]||"",!0),this.H=il(b[2]||""),this.g=il(b[3]||"",!0),fl(this,b[4]),gl(this,b[5]||"",!0),hl(this,b[6]||"",!0),this.C=il(b[7]||"")):(this.v=!1,this.l=new jl(null,this.v))}
dl.prototype.toString=function(){var a=[],b=this.o;b&&a.push(kl(b,ll,!0),":");var c=this.g;if(c||b=="file")a.push("//"),(b=this.H)&&a.push(kl(b,ll,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.B,c!=null&&a.push(":",String(c));if(c=this.j)this.g&&c.charAt(0)!="/"&&a.push("/"),a.push(kl(c,c.charAt(0)=="/"?ml:nl,!0));(c=this.l.toString())&&a.push("?",c);(c=this.C)&&a.push("#",kl(c,ol));return a.join("")};
dl.prototype.resolve=function(a){var b=this.clone(),c=!!a.o;c?el(b,a.o):c=!!a.H;c?b.H=a.H:c=!!a.g;c?b.g=a.g:c=a.B!=null;var d=a.j;if(c)fl(b,a.B);else if(c=!!a.j){if(d.charAt(0)!="/")if(this.g&&!this.j)d="/"+d;else{var e=b.j.lastIndexOf("/");e!=-1&&(d=b.j.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):h==".."?((f.length>1||f.length==1&&
f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?gl(b,d):c=a.l.toString()!=="";c?hl(b,a.l.clone()):c=!!a.C;c&&(b.C=a.C);return b};dl.prototype.clone=function(){return new dl(this)};function el(a,b,c){a.o=c?il(b,!0):b;a.o&&(a.o=a.o.replace(/:$/,""))}function fl(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.B=b}else a.B=null}function gl(a,b,c){a.j=c?il(b,!0):b;return a}
function hl(a,b,c){b instanceof jl?(a.l=b,pl(a.l,a.v)):(c||(b=kl(b,ql)),a.l=new jl(b,a.v))}function il(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function kl(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,rl),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function rl(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var ll=/[#\/\?@]/g,nl=/[#\?:]/g,ml=/[#\?]/g,ql=/[#\?@]/g,ol=/#/g;
function jl(a,b){this.j=this.g=null;this.l=a||null;this.o=!!b}function sl(a){a.g||(a.g=new Map,a.j=0,a.l&&oi(a.l,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}q=jl.prototype;q.add=function(a,b){sl(this);this.l=null;a=tl(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.j=this.j+1;return this};q.remove=function(a){sl(this);a=tl(this,a);return this.g.has(a)?(this.l=null,this.j=this.j-this.g.get(a).length,this.g.delete(a)):!1};
q.clear=function(){this.g=this.l=null;this.j=0};function ul(a,b){sl(a);b=tl(a,b);return a.g.has(b)}q.forEach=function(a,b){sl(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};q.ta=function(a){sl(this);var b=[];if(typeof a==="string")ul(this,a)&&(b=b.concat(this.g.get(tl(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
q.set=function(a,b){sl(this);this.l=null;a=tl(this,a);ul(this,a)&&(this.j=this.j-this.g.get(a).length);this.g.set(a,[b]);this.j=this.j+1;return this};q.get=function(a,b){if(!a)return b;a=this.ta(a);return a.length>0?String(a[0]):b};
q.toString=function(){if(this.l)return this.l;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.ta(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.l=a.join("&")};q.clone=function(){var a=new jl;a.l=this.l;this.g&&(a.g=new Map(this.g),a.j=this.j);return a};function tl(a,b){b=String(b);a.o&&(b=b.toLowerCase());return b}
function pl(a,b){b&&!a.o&&(sl(a),a.l=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(this.remove(d),this.remove(e),c.length>0)){this.l=null;d=this.g;var f=d.set;e=tl(this,e);var g=c.length;if(g>0){for(var h=Array(g),l=0;l<g;l++)h[l]=c[l];g=h}else g=[];f.call(d,e,g);this.j=this.j+c.length}},a));a.o=b};function vl(){var a=y.window;a.onbeforeunload=k();a.location.reload()};function wl(){this.g=function(){vl()}}wl.prototype.notify=function(){window.confirm("This error has been reported to Google and we'll look into it as soon as possible. Please reload this page to continue.")&&this.g()};function zl(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.ma=!1}zl.prototype.stopPropagation=function(){this.ma=!0};zl.prototype.preventDefault=function(){this.defaultPrevented=!0};var Al=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=k();y.addEventListener("test",c,b);y.removeEventListener("test",c,b)}catch(d){}return a}();function Bl(a,b){zl.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.g=null;a&&this.init(a,b)}z(Bl,zl);
Bl.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=a.offsetX,this.offsetY=a.offsetY,this.clientX=
a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.g=a;a.defaultPrevented&&Bl.ba.preventDefault.call(this)};
Bl.prototype.stopPropagation=function(){Bl.ba.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};Bl.prototype.preventDefault=function(){Bl.ba.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var Cl="closure_listenable_"+(Math.random()*1E6|0);var Dl=0;function El(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.va=e;this.key=++Dl;this.removed=this.ra=!1}function Fl(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.va=null};function Gl(a){this.src=a;this.g={};this.j=0}Gl.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.j++);var g=Hl(a,b,d,e);g>-1?(b=a[g],c||(b.ra=!1)):(b=new El(b,this.src,f,!!d,e),b.ra=c,a.push(b));return b};Gl.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=Hl(e,b,c,d);return b>-1?(Fl(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.g[a],this.j--),!0):!1};
function Il(a,b){var c=b.type;c in a.g&&cb(a.g[c],b)&&(Fl(b),a.g[c].length==0&&(delete a.g[c],a.j--))}Gl.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,Fl(d[e]);delete this.g[c];this.j--}return b};function Hl(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.va==d)return e}return-1};var Jl="closure_lm_"+(Math.random()*1E6|0),Kl={},Ll=0;function Ml(a,b,c,d,e){if(d&&d.once)return Nl(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Ml(a,b[f],c,d,e);return null}c=Ol(c);return a&&a[Cl]?a.listen(b,c,Ca(d)?!!d.capture:!!d,e):Pl(a,b,c,!1,d,e)}
function Pl(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Ca(e)?!!e.capture:!!e,h=Ql(a);h||(a[Jl]=h=new Gl(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Rl();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Al||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Sl(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Ll++;return c}
function Rl(){function a(c){return b.call(a.src,a.listener,c)}var b=Tl;return a}function Nl(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Nl(a,b[f],c,d,e);return null}c=Ol(c);return a&&a[Cl]?a.j.add(String(b),c,!0,Ca(d)?!!d.capture:!!d,e):Pl(a,b,c,!0,d,e)}
function Ul(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ul(a,b[f],c,d,e);else(d=Ca(d)?!!d.capture:!!d,c=Ol(c),a&&a[Cl])?a.j.remove(String(b),c,d,e):a&&(a=Ql(a))&&(b=a.g[b.toString()],a=-1,b&&(a=Hl(b,c,d,e)),(c=a>-1?b[a]:null)&&Vl(c))}
function Vl(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[Cl])Il(b.j,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Sl(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Ll--;(c=Ql(b))?(Il(c,a),c.j==0&&(c.src=null,b[Jl]=null)):Fl(a)}}}function Sl(a){return a in Kl?Kl[a]:Kl[a]="on"+a}
function Tl(a,b){if(a.removed)a=!0;else{b=new Bl(b,this);var c=a.listener,d=a.va||a.src;a.ra&&Vl(a);a=c.call(d,b)}return a}function Ql(a){a=a[Jl];return a instanceof Gl?a:null}var Wl="__closure_events_fn_"+(Math.random()*1E9>>>0);function Ol(a){if(typeof a==="function")return a;a[Wl]||(a[Wl]=function(b){return a.handleEvent(b)});return a[Wl]}Di(function(a){Tl=a(Tl)});function Xl(a,b){zl.call(this,a);this.error=b}t(Xl,zl);var Yl=/\/d\/([^\/]+)/,Zl=/\/r\/([^\/]+)/;function $l(a){a=a.match(ni)[5]||null;return Yl.test(a)}function am(a,b){if($l(a)){$l(a);var c=a.match(ni),d=c[5];d=d.replace(b,"");b=c[1];a=c[2];var e=c[3],f=c[4],g=c[6];c=c[7];var h="";b&&(h+=b+":");e&&(h+="//",a&&(h+=a+"@"),h+=e,f&&(h+=":"+f));d&&(h+=d);g&&(h+="?"+g);c&&(h+="#"+c);b=h}else b=a;return b};function bm(){Y.call(this);this.j=new Gl(this);this.Za=this;this.U=null}z(bm,Y);bm.prototype[Cl]=!0;q=bm.prototype;q.addEventListener=function(a,b,c,d){Ml(this,a,b,c,d)};q.removeEventListener=function(a,b,c,d){Ul(this,a,b,c,d)};
q.dispatchEvent=function(a){var b=this.U;if(b){var c=[];for(var d=1;b;b=b.U)c.push(b),++d}b=this.Za;d=a.type||a;if(typeof a==="string")a=new zl(a,b);else if(a instanceof zl)a.target=a.target||b;else{var e=a;a=new zl(d,b);cl(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.ma&&f>=0;f--){var g=a.currentTarget=c[f];e=cm(g,d,!0,a)&&e}a.ma||(g=a.currentTarget=b,e=cm(g,d,!0,a)&&e,a.ma||(e=cm(g,d,!1,a)&&e));if(c)for(f=0;!a.ma&&f<c.length;f++)g=a.currentTarget=c[f],e=cm(g,d,!1,a)&&e;return e};
q.M=function(){bm.ba.M.call(this);this.j&&this.j.removeAll(void 0);this.U=null};q.listen=function(a,b,c,d){return this.j.add(String(a),b,!1,c,d)};function cm(a,b,c,d){b=a.j.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,l=g.va||g.src;g.ra&&Il(a.j,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};function dm(a,b){if(typeof a!=="function")if(a&&typeof a.handleEvent=="function")a=Ha(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:y.setTimeout(a,b||0)}function em(){var a=null;return(new Pi(function(b,c){a=dm(function(){b(void 0)},14E3);a==-1&&c(Error("Failed to schedule timer."))})).za(function(b){y.clearTimeout(a);throw b;})};function fm(a,b,c){Y.call(this);this.g=a;this.l=b||0;this.j=c;this.o=Ha(this.ob,this)}z(fm,Y);q=fm.prototype;q.ja=0;q.M=function(){fm.ba.M.call(this);this.stop();delete this.g;delete this.j};q.start=function(a){this.stop();this.ja=dm(this.o,a!==void 0?a:this.l)};q.stop=function(){this.isActive()&&y.clearTimeout(this.ja);this.ja=0};q.isActive=function(){return this.ja!=0};q.ob=function(){this.ja=0;this.g&&this.g.call(this.j)};function gm(a,b,c,d){Y.call(this);this.l=d!=null?d:.15;this.v=a;this.o=b;this.F=c;this.g=new fm(this.zb,void 0,this);this.B=Number.NEGATIVE_INFINITY;this.j=0}t(gm,Y);q=gm.prototype;q.isActive=function(){return this.g.isActive()};q.start=function(){hm(this,!1,!1)};function hm(a,b,c){b&&(a.g.stop(),im(a,a.o));a.isActive()||(b=Math.max(0,a.B+a.j-Date.now()),b==0&&(c?b=im(a,a.o):a.j=0),a.g.start(b))}q.stop=function(){this.g.stop()};
function im(a,b){b>0&&a.l!=0&&(b=Math.floor(b*(1-a.l+Math.random()*a.l*2)));return a.j=b}q.zb=function(){this.B=Date.now();im(this,Math.min(Math.max(this.j*2,this.o),this.F));this.v()};q.M=function(){this.g.dispose();delete this.g;delete this.v;Y.prototype.M.call(this)};function jm(a){Y.call(this);this.j=a;this.g={}}z(jm,Y);var km=[];jm.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(km[0]=b.toString()),b=km);for(var e=0;e<b.length;e++){var f=Ml(a,b[e],c||this.handleEvent,d||!1,this.j||this);if(!f)break;this.g[f.key]=f}return this};jm.prototype.removeAll=function(){$k(this.g,function(a,b){this.g.hasOwnProperty(b)&&Vl(a)},this);this.g={}};jm.prototype.M=function(){jm.ba.M.call(this);this.removeAll()};
jm.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function lm(a,b,c,d,e,f,g){g=g===void 0?!0:g;Y.call(this);var h=this;this.g=a;this.g.O=1E4;this.da=b;this.l=f;this.j=new gm(function(){return h.Ga()},3E4,36E5);this.v=0;this.F=null;this.V=new Lj("errorsender",1,8,d);wi(this,this.V);this.U=!1;this.L=null;this.B=new Set;this.J=new jm(this);this.pa=c||10;this.Z=e||null;this.J.listen(this.g,"complete",this.xb);this.J.listen(this.g,"ready",this.Ga);this.R=null;this.N=new bk;wi(this,this.N);this.l&&ck(this.N,this.l.j(),function(){h.l.getState().g>=3&&(h.R=
(Yj(),Xj));h.l.getState().g>=3||h.R!==(Yj(),Xj)||mm(h)});this.O=g}t(lm,Y);q=lm.prototype;q.send=function(a,b,c,d){Gj(this.da.get("docs-dafjera"))&&(a=am(am(a,Zl),Yl));var e=sj(sj(zj(this.o.length),function(f){if(!(f>=this.pa))return this.O&&(a=ti(a,"errorSender_enqueueTimeMs",Date.now().toString())),f={},f.u=a,f.m=b,f.c=c,f.h=d,this.enqueue(f)},this),this.Ga,this);uj(e,function(){this.B.delete(e)},this);this.B.add(e)};function nm(a){return Yi(Array.from(a.B.values())).then(k())}
q.Ga=function(){var a=this.l&&this.l.getState().g>=3,b=this.wa()||this.g.isActive()||this.j.isActive()||this.U;return a||b?zj():om(this)};function om(a){return function(){return sj(zj(a.o[0]!==void 0?a.o[0]:null),function(b){return pm(a,b)})}()}
function pm(a,b){if(a.j.isActive()||a.g.isActive()||a.U)return zj();if(!b)return a.j.stop(),zj();if(b.u.length>4E3)return qm(a);try{var c=a.V;if(!((c.j.get(null)+1|0)/S(c.j.l/1E3)<=c.l))throw(new Kj("Query would cause "+J(c.v)+" to exceed "+c.l+" qps.")).g;var d=c.j,e=Id(Pd(Date.now()));Rj(d,e);var f=N(Tj(d.g),Oj,Nj);if(f==null||S(e)>=S(f.j)){var g=Sj(d,S(e)),h=new Nj;h.j=g;h.g=0;h.o=2147483647;h.l=-2147483648;f=h;d.g.add(f)}f.g=f.g+1|0;f.o=Math.min(1,f.o);f.l=Math.max(1,f.l);a.L=new lj;var l=b.u;
a.Z!=null&&(l=ti(l,"reportingSessionId",a.Z));a.v>0&&(l=ti(l,"retryCount",a.v));a.F!=null&&(l=ti(l,"previousErrorSendStatus",a.F));a.O&&(l=ti(l,"errorSenderType",a.Ra()),b.errorSender_frontIndex&&(l=ti(l,"errorSender_frontIndex",b.errorSender_frontIndex)),b.errorSender_nextIndex&&(l=ti(l,"errorSender_nextIndex",b.errorSender_nextIndex)),b.errorSender_queueSize&&(l=ti(l,"errorSender_queueSize",b.errorSender_queueSize)));a.g.send(l,b.m,b.c,b.h);return a.L}catch(m){b=m;if(b==null)b=new be,ce(b),M(b,
Error(b));else if(pe(b))b=N(b,pe,be);else if(wh(b))b=N(b,wh,vh),b=je(b);else throw De("Unsupported type cannot be used to create a Throwable.").g;if(b instanceof Kj)a.U=!0;else throw ci(m,{"docs-origin-class":"docs.debug.ErrorSender"});}return zj()}q.xb=function(){var a=rm(this.g),b=this.L,c=sm(this.g)||a>=400&&a<=500,d=this.v>3;c||d?(this.v=0,this.F=null,this.j.stop(),sj(qm(this),function(){nj(b);oj(b,!0)})):(this.v++,this.F=a===-1?this.g.B:a,mm(this),nj(b),oj(b,!0))};
function mm(a){a.v!=1||a.j.isActive()?a.j.start():hm(a.j,!0,!0)}q.M=function(){vi(this.J,this.j,this.g,this.N);this.B.clear();Y.prototype.M.call(this)};q.Ra=ba("BaseErrorSender");function tm(a,b,c,d,e){lm.call(this,a,b,c,void 0,d,e,void 0);this.o=[]}t(tm,lm);tm.prototype.enqueue=function(a){this.o.push(a);return zj()};function qm(a){a.o.shift();return zj()}tm.prototype.Ra=ba("MemoryErrorSender");tm.prototype.M=function(){delete this.o;lm.prototype.M.call(this)};function um(){var a=document;var b="IFRAME";a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};function vm(a){this.g=td(Xd(),Kc(a));a=md(this.g,1);this.j=Math.floor(Math.random()*100)<a}vm.prototype.toString=function(){var a="{bool="+!(this.j?!ld(this.g,5):!ld(this.g,2))+', string="',b=this.j?qd(this.g,6):od(this.g,3);a=a+(b!=null?String(b):"")+'", int=';b=this.j?tc(E(this.g,7,void 0,Vc)):md(this.g,4,-1);return a+(b!=null?Number(b):-1)+"}"};function wm(a){this.g=new Map;this.j=[];if(a=a.get("docs-cei")){var b=a.i;b&&db(this.j,b);a=a.cf||{};for(var c in a)this.g.set(c,new vm(a[c]))}}wm.prototype.get=function(a){return this.g.get(a)||null};function xm(){for(var a in Array.prototype)return!1;return!0};function ym(a){this.g=a}function zm(a){var b=a.g;if(b==null)return null;if(typeof b==="string")return b;throw new TypeError("Invalid string data <K1cgmc>: "+a.g+" (typeof "+typeof a.g+")");}ym.prototype.toString=function(){var a=zm(this);if(a===null)throw Error("Data K1cgmc not defined.");return a};function Am(a){this.D=D(a)}t(Am,F);function Bm(a){this.D=D(a)}t(Bm,F);var Cm=[4,5];function Dm(a){this.D=D(a)}t(Dm,F);function Em(){var a=y;a=a===void 0?window:a;var b=new ym(Rd("K1cgmc",a));a=new Dm;b=zm(b);a=b===null?a:sd(Dm,"["+b.substring(4));b=a.D;var c=b[B]|0;this.g=Kb(a,c)?a:Oc(a,b,c)?Pc(a,b):new a.constructor(Nc(b,c,!0))}
Em.prototype.ka=function(){var a=new Map,b;(b=this.g)==null?b=void 0:(b=hd(b,Bm,1),b=hd(b,Am,ed(b,Cm,4)));if(b==null?0:sc(E(b,2))!=null){var c,d=(c=pd(b,2))==null?void 0:c.toString();d&&a.set("canaryanalysisservertestgroup",d);if(b==null)var e=void 0;else if((c=id(b,Ud,3))==null)e=void 0;else{b=Number;e=e===void 0?"0":e;d=E(c,1);var f=!0;f=f===void 0?!1:f;var g=typeof d;d!=null&&(g==="bigint"?d=String(jc(64,d)):qc(d)?g==="string"?(qc(d),f=mc(Number(d)),kc(f)?d=String(f):(f=d.indexOf("."),f!==-1&&
(d=d.substring(0,f)),d=vc(d))):d=f?xc(d):wc(d):d=void 0);e=b(d!=null?d:e);c=md(c,2);e=(new Date(e*1E3+c/1E6)).valueOf().toString()}e&&a.set("serverstarttimemillis",e)}var h,l;(e=(h=this.g)==null?void 0:(l=id(h,Bm,1))==null?void 0:pd(l,6))&&a.set("clientApp",String(e));return a};function Fm(){}Fm.prototype.ka=function(){var a=new Map;Gm()&&a.set("apps_telemetry.screen_tampered","true");a:{var b=v(Array.prototype);for(b=b.next();!b.done;b=b.next()){b=!0;break a}b=!1}b&&a.set("apps_telemetry.array_prototype_tampered","true");return a};function Gm(){if("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)return!1;var a=y.screen,b=!(a instanceof Screen);try{var c=k();a.addEventListener("change",c);a.removeEventListener("change",c)}catch(d){b=!0}return b};function Hm(){}Hm.prototype.ka=function(){if("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)return new Map;try{var a=Array.from(document.querySelectorAll("script")).filter(this.j).slice(0,30).map(this.g).join("\n")}catch(b){a="Error getting cross-origin scripts"}return(new Map).set("apps_telemetry.cross_origin_scripts",a)};
Hm.prototype.j=function(a){var b=new RegExp(/^(?:https?:\/\/)?(?:[a-zA-Z0-9-]+\.)*google\.com(?:$|[\/#?])/);return(a=a.getAttribute("src"))?!(a.startsWith("/")||b.test(a)):!1};Hm.prototype.g=function(a){return a.innerHTML?a.outerHTML.slice(0,a.outerHTML.indexOf(a.innerHTML)):a.outerHTML};function Im(a){return a instanceof Error||a&&a.message!==void 0?a.message:Jm(a)}function Km(a){return a instanceof Error||a&&a.stack!==void 0?a.stack||"":""}function Lm(a,b){var c=a&&a.cause!==void 0;if(b>=3||!c)return null;c=Dk(new Bk,"");a=a.cause;if(a instanceof Error||a.message!==void 0&&a.stack!==void 0){Ck(c,Im(a));var d=Km(a);hf(d);c.g=d;if(b=Lm(a,b+1))c.j=b}else Ck(c,Jm(a));return Ek(c)}
function Jm(a){try{return a&&a instanceof Object?JSON.stringify(a):String(a)}catch(b){return String(a)}};/*

Math.uuid.js (v1.4)
http://www.broofa.com
mailto:<EMAIL>
Copyright (c) 2010 Robert Kieffer
Dual licensed under the MIT and GPL licenses.
*/
var Mm="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function Nm(a){var b=void 0;b=b===void 0?[]:b;try{var c=Sd=Sd||new Td;var d=Vd.key in c.g?Vd.ctor(c.g[Vd.key]):Vd.defaultValue,e=void 0===Mb?2:4;c=void 0;var f=d.D,g=f[B]|0,h=Kb(d,g)?1:e;c=!!c||h===3;h===2&&Rc(d)&&(f=d.D,g=f[B]|0);var l=bd(f,1),m=l===Db?7:l[B]|0,n=cd(m,g);if(d=4&n?!1:!0){4&n&&(l=Array.prototype.slice.call(l),m=0,n=ad(n,g),g=Yc(f,g,1,l));for(var u=e=0;e<l.length;e++){var x=yc(l[e]);x!=null&&(l[u++]=x)}u<e&&(l.length=u);x=n|=4;x&=-513;n=x&-1025;n&=-4097}n!==m&&(Fb(l,n),2&n&&Object.freeze(l));
var w=l=Zc(l,n,f,g,1,h,d,c)}catch(L){w=[]}a=a===void 0?[]:a;f=b;g=w;b=[Error("uncaught error").message];Sk();w=If();if(g.length!=0){h=w.add;l=If();for(m=0;m<g.length;m=m+1|0)l.add(X(N(g[m],rg,bf)));h.call(w,Uk(l,l,7))}w.Aa(Ik);w.add(Jk);w.add(Kk);w.add(Lk);w.add(Mk);w.add(Nk);w.add(Ok);w.add(Pk);for(g=0;g<f.length;g=g+1|0)w.add(N(f[g],vk,uk));if(b.length!=0){f=If();for(g=0;g<b.length;g=g+1|0)f.add(N(b[g],rg,bf));b=w.add;g=new Yk;h=If();g.j=3;g.g=5;g.l=f;g.C=h;g.o=!0;b.call(w,g)}b=new Hk;b.g=!1;b.j=
w;this.l=b;this.g=[new Em,new Fm,new Hm];this.g.push.apply(this.g,oa(a));a=[];a[8]=a[13]=a[18]=a[23]="-";a[14]="4";for(w=0;w<36;w++)a[w]||(b=0|Math.random()*16,a[w]=Mm[w==19?b&3|8:b]);this.j=a.join("")}function Om(a,b,c,d){d["apps_telemetry.session_id"]=a.j;"apps_telemetry.processed"in d&&(d["apps_telemetry.multi_processed"]="true");var e=a.ka();(a=Pm(a,b,c,e))&&Qm(e,a.g);e.forEach(function(g,h){d[h]=g});var f;return(f=a==null?void 0:a.j)!=null?f:c}
function Pm(a,b,c,d){try{var e=Ck(Dk(new Bk,""),Im(b)),f=Km(b);hf(f);e.g=f;var g=Lm(b,0);g&&(e.j=g);c&&Dk(e,c);var h=Ek(e);var l=a.l,m=h.o,n=Yf();try{l.g&&n.X("apps_telemetry.after_downgraded_severe","true");for(a=0;a<l.j.size();a=a+1|0){var u=N(l.j.Y(a),vk,uk);if(u.v(h)){var x=u.j,w=u.g,L=new Ak;hf(x);L.j=x;hf(w);L.g=w;var O=L}else O=null;var Q=O;if(Q!=null){h=m;u=void 0;O=l;x=m;w=Qk;var Wa=w.contains,Xa=(lh(),kh);u=I(Xa,jh)?x.toLocaleUpperCase():x.toUpperCase();if(Wa.call(w,u)){O.g=!0;var pb="WARNING"}else pb=
x;Wa=h;Xa=pb;var Ob=Yf();Ob.X("apps_telemetry.classification",""+Q.j);Ob.X("apps_telemetry.classification_code",Q.g!=null?""+Q.g:"");Ob.X("apps_telemetry.incoming_severity",Wa);Ob.X("apps_telemetry.outgoing_severity",Xa);Q=n;S(Ob);for(var xl=Ob.aa().G();xl.g();){var yl=N(xl.j(),V,W);Q.X(yl.P(),yl.S())}m=pb;break}}n.X("apps_telemetry.processed","true")}catch(sh){var th=je(sh);if(th instanceof P)n.X("apps_telemetry.processed","false"),n.X("apps_telemetry.handling_error",Rk(l,th,0));else throw th.g;
}l=m;var Sc=new Vk;hf(l);Sc.j=l;hf(n);Sc.g=n;return Sc}catch(sh){Rm(d,sh,"apps_telemetry.processed")}return null}Nm.prototype.ka=function(){var a=new Map;try{for(var b=v(this.g),c=b.next();!c.done;c=b.next())c.value.ka().forEach(function(d,e){a.set(e,d)})}catch(d){Rm(a,d,"apps_telemetry.annotated")}return a};function Qm(a,b){b.Ta().Wa().forEach(function(c){a.set(c,b.get(c))})}function Rm(a,b,c){a.set(c,"false");a.set("apps_telemetry.handling_error",Jm(b))};y.U3bHHf!=null||(y.U3bHHf=0);y.U3bHHf++;function Sm(a,b){var c=a.__wiz;c||(c=a.__wiz={});return c[b.toString()]};/*

 Copyright 2024 Google, Inc
 SPDX-License-Identifier: MIT
*/
var Tm={};var Um={};function Vm(a){var b=document.body,c=Na(b.getAttribute("jsaction")||"");var d=["u0pjoe"];for(var e=v(d),f=e.next();!f.done;f=e.next()){f=f.value;var g;if(g=c){var h=Tm[g];h?g=!!h[f.toString()]:(h=Um[f.toString()],h||(h=new RegExp("(^\\s*"+f+"\\s*:|[\\s;]"+f+"\\s*:)"),Um[f.toString()]=h),g=h.test(g))}else g=!1;g||(c&&!/;$/.test(c)&&(c+=";"),c+=f+":.CLIENT",Wm(b,c));(g=Sm(b,f))?g.push(a):b.__wiz[f.toString()]=[a]}return{et:d,lb:a,el:b}}
function Wm(a,b){a.setAttribute("jsaction",b);"__jsaction"in a&&delete a.__jsaction};function Xm(a){Y.call(this);this.j=a}z(Xm,Y);Xm.prototype.g=function(a){return Ym(this,a)};function Zm(a,b){a=Object.prototype.hasOwnProperty.call(a,Da)&&a[Da]||(a[Da]=++Ea);return(b?"__wrapper_":"__protected_")+a+"__"}function Ym(a,b){var c=Zm(a,!0);b[c]||((b[c]=$m(a,b))[Zm(a,!1)]=b);return b[c]}function $m(a,b){function c(){if(a.wa())return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){an(a,d)}}c[Zm(a,!1)]=b;return c}
function an(a,b){if(!(b&&typeof b==="object"&&typeof b.message==="string"&&b.message.indexOf("Error in protected function: ")==0||typeof b==="string"&&b.indexOf("Error in protected function: ")==0))throw a.j(b),new bn(b);}function cn(a){var b=b||y.window||y.globalThis;"onunhandledrejection"in b&&(b.onunhandledrejection=function(c){an(a,c&&c.reason?c.reason:Error("uncaught error"))})}
function dn(a,b){var c=y.window||y.globalThis,d=c[b];if(!d)throw Error(b+" not on global?");c[b]=function(e,f){typeof e==="string"&&(e=Ia(Ja,e));e&&(arguments[0]=e=Ym(a,e));if(d.apply)return d.apply(this,arguments);var g=e;if(arguments.length>2){var h=Array.prototype.slice.call(arguments,2);g=function(){e.apply(this,h)}}return d(g,f)};c[b][Zm(a,!1)]=d}
Xm.prototype.M=function(){var a=y.window||y.globalThis;var b=a.setTimeout;b=b[Zm(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[Zm(this,!1)]||b;a.setInterval=b;Xm.ba.M.call(this)};function bn(a){La.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&typeof a==="string"&&(this.stack=a)}z(bn,La);function en(){bm.call(this);this.headers=new Map;this.l=!1;this.g=null;this.N="";this.B=0;this.v=this.L=this.F=this.J=!1;this.O=0;this.o=null;this.R="";this.V=!1}z(en,bm);var fn=/^https?$/i,gn=["POST","PUT"],hn=[];q=en.prototype;q.mb=function(){this.dispose();cb(hn,this)};
q.send=function(a,b,c,d){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.N+"; newUri="+a);b=b?b.toUpperCase():"GET";this.N=a;this.B=0;this.J=!1;this.l=!0;this.g=new XMLHttpRequest;this.g.onreadystatechange=xi(Ha(this.Va,this));try{this.L=!0,this.g.open(b,String(a),!0),this.L=!1}catch(g){jn(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get===
"function"){e=v(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=y.FormData&&a instanceof y.FormData;!(Array.prototype.indexOf.call(gn,b,void 0)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=v(c);for(d=b.next();!d.done;d=b.next())c=v(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,
c);this.R&&(this.g.responseType=this.R);"withCredentials"in this.g&&this.g.withCredentials!==this.V&&(this.g.withCredentials=this.V);try{this.o&&(clearTimeout(this.o),this.o=null),this.O>0&&(this.o=setTimeout(this.Ab.bind(this),this.O)),this.F=!0,this.g.send(a),this.F=!1}catch(g){jn(this)}};q.Ab=function(){typeof xa!="undefined"&&this.g&&(this.B=8,this.dispatchEvent("timeout"),this.abort(8))};function jn(a){a.l=!1;a.g&&(a.v=!0,a.g.abort(),a.v=!1);a.B=5;kn(a);ln(a)}
function kn(a){a.J||(a.J=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))}q.abort=function(a){this.g&&this.l&&(this.l=!1,this.v=!0,this.g.abort(),this.v=!1,this.B=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),ln(this))};q.M=function(){this.g&&(this.l&&(this.l=!1,this.v=!0,this.g.abort(),this.v=!1),ln(this,!0));en.ba.M.call(this)};q.Va=function(){this.wa()||(this.L||this.F||this.v?mn(this):this.Ha())};q.Ha=function(){mn(this)};
function mn(a){if(a.l&&typeof xa!="undefined")if(a.F&&(a.g?a.g.readyState:0)==4)setTimeout(a.Va.bind(a),0);else if(a.dispatchEvent("readystatechange"),(a.g?a.g.readyState:0)==4){a.l=!1;try{sm(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.B=6,kn(a))}finally{ln(a)}}}function ln(a,b){if(a.g){a.o&&(clearTimeout(a.o),a.o=null);var c=a.g;a.g=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}}q.isActive=function(){return!!this.g};
function sm(a){var b=rm(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.N).match(ni)[1]||null,!a&&y.self&&y.self.location&&(a=y.self.location.protocol.slice(0,-1)),b=!fn.test(a?a.toLowerCase():"");c=b}return c}function rm(a){try{return(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(b){return-1}}Di(function(a){en.prototype.Ha=a(en.prototype.Ha)});function nn(a,b,c){bm.call(this);this.v=b||null;this.o={};this.B=on;this.J=a;if(!c){this.g=null;this.g=new Xm(Ha(this.l,this));dn(this.g,"setTimeout");dn(this.g,"setInterval");a=this.g;b=y.window||y.globalThis;c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame","msRequestAnimationFrame"];for(var d=0;d<c.length;d++){var e=c[d];c[d]in b&&dn(a,e)}a=this.g;Ci=!0;b=Ha(a.g,a);for(c=0;c<Ai.length;c++)Ai[c](b);Bi.push(a)}}z(nn,bm);
function pn(a,b){zl.call(this,"c");this.error=a;this.ea=b}z(pn,zl);function qn(a,b){return new nn(a,b,void 0)}function on(a,b,c,d){if(d instanceof Map){var e={};d=v(d);for(var f=d.next();!f.done;f=d.next()){var g=v(f.value);f=g.next().value;g=g.next().value;e[f]=g}}else e=d;d=new en;hn.push(d);d.j.add("ready",d.mb,!0,void 0,void 0);d.send(a,b,c,e)}function rn(a,b){a.B=b}
nn.prototype.l=function(a,b){a=a.error||a;b=b?al(b):{};a instanceof Error&&cl(b,ob(a));var c=Xh(a);if(this.v)try{this.v(c,b)}catch(x){}var d=c.message.substring(0,1900);if(!(a instanceof La)||a.g){var e=c.fileName,f=c.lineNumber;a=c.stack;try{var g=ti(this.J,"script",e,"error",d,"line",f);a:{for(var h in this.o){var l=!1;break a}l=!0}if(!l){l=g;var m=si(this.o);g=pi(l,m)}m={};m.trace=a;if(b)for(var n in b)m["context."+n]=b[n];var u=si(m);this.B(g,"POST",u,this.F)}catch(x){}}try{this.dispatchEvent(new pn(c,
b))}catch(x){}};nn.prototype.M=function(){ui(this.g);nn.ba.M.call(this)};function sn(){this.g=Date.now()}var tn=null;sn.prototype.set=function(a){this.g=a};sn.prototype.reset=function(){this.set(Date.now())};sn.prototype.get=p("g");function un(a){this.o=a||"";tn||(tn=new sn);this.v=tn}un.prototype.g=!0;un.prototype.j=!0;un.prototype.l=!1;function vn(a){return a<10?"0"+a:String(a)}function wn(a){un.call(this,a)}z(wn,un);
function xn(a,b){var c=[];c.push(a.o," ");if(a.j){var d=c.push,e=new Date(b.l());d.call(c,"[",vn(e.getFullYear()-2E3)+vn(e.getMonth()+1)+vn(e.getDate())+" "+vn(e.getHours())+":"+vn(e.getMinutes())+":"+vn(e.getSeconds())+"."+vn(Math.floor(e.getMilliseconds()/10)),"] ")}d=c.push;e=a.v.get();e=(b.l()-e)/1E3;var f=e.toFixed(3),g=0;if(e<1)g=2;else for(;e<100;)g++,e*=10;for(;g-- >0;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.j(),"] ");c.push(b.getMessage());a.l&&(b=b.g(),b!==void 0&&c.push("\n",b instanceof
Error?b.message:String(b)));a.g&&c.push("\n");return c.join("")};function yn(a){a=a===void 0?new zn:a;bm.call(this);var b=this;this.R={};this.g=null;this.l={};this.N=new jm(this);this.pb=a.C;this.V=a.L;this.eb=a.F;this.nb=a.v;this.fb=a.N;var c=a.j;this.bb=new Nm(a.J);this.kb=a.U;this.Z=new wl;var d=new en;An(this,c);this.B=new tm(d,c,void 0,void 0,void 0);wi(this,this.B);this.o=a.g?a.g:Jj(c,"docs-sup")+Jj(c,"docs-jepp")+"/jserror";if(d=Jj(c,"jobset"))this.o=ti(this.o,"jobset",d);if(d=Jj(c,"docs-ci"))this.o=ti(this.o,"id",d);d=Jj(c,"docs-pid");Gj(c.get("docs-eaotx"))&&
d&&(this.o=ti(this.o,"ouid",d));this.Oa=Ij(c,"docs-srmoe")||0;this.ib=Gj(c.get("docs-oesf"));this.Pa=Ij(c,"docs-srmour")||0;this.jb=Gj(c.get("docs-oursf"));d=a.o||this.Pa>0&&Math.random()<this.Pa;this.gb=Gj(c.get("docs-wesf"));this.Qa=Ij(c,"docs-srmwe")||0;Bn(this);kj=function(g){return Cn(b,g,"promise rejection")};var e=Ij(c,"docs-srmdue")||0;if(e>0&&Math.random()<e){var f=Gj(c.get("docs-duesf"));rj=function(g){Cn(b,g,"deferred error",f,"isDeferredUnhandledErrback")}}else rj=k();Ij(c,"docs-srmxue");
c.get("docs-xduesf");d&&(d=new Xm(function(g){var h={};h=(h.isUnhandledRejection="true",h);b.jb?Dn(b,g,h):b.info(g,h)}),cn(d),wi(this,d));this.L=null;this.Qa>0&&Math.random()<this.Qa&&document&&document.body&&(this.L=Vm(function(g){var h={};h=(h.isWizError="true",h);g=v(g.data.errors);for(var l=g.next();!l.done;l=g.next())l=l.value.error,b.gb?Dn(b,l,h):b.info(l,h)}));this.O=a.l;this.F=!1;this.J=!0;this.v=!1;this.pa=Jj(c,"docs-jern");this.cb=a.B;this.ab=a.H.concat(Object.values(Kh))}t(yn,bm);
function Bn(a){var b=b===void 0?!1:b;if(En){if(Fn!=null)throw Error('ErrorReporter already installed. at "'+Fn.stack+'"');throw Error("ErrorReporter already installed.");}En=!0;Fn=Error();a.g=qn(a.o,function(e,f){return Gn(a,e,f)});var c={};a.eb&&(c["X-No-Abort"]="1");a.g.F=c;rn(a.g,function(e,f,g,h){a.J&&a.B.send(e,f,g,h)});if(a.Oa>0&&Math.random()<a.Oa){c={};var d=(c.isWindowOnError="true",c);a.ib?Wh(function(e){Dn(a,e.error instanceof Error?e.error:Error(e.message),d)}):Wh(function(e){a.log(e.error instanceof
Error?e.error:Error(e.message),d)})}a.N.listen(a.g,"c",function(e){e.ea.severity=e.ea["severity-unprefixed"]||e.ea.severity;var f=e.ea.severity;(f=f=="fatal"||f=="postmortem")&&!a.nb&&(!a.pb||(b===void 0?0:b)?a.Z.notify(void 0,e.ea):a.Z.notify(e,e.ea));a.dispatchEvent(new Xl(f?"a":"b",e.error,e.ea))})}function An(a,b){b=new wm(b);var c=b.g,d;for(d in c){var e=c[d];e&&(a.l["expflag-"+d]=e.toString())}a.l.experimentIds=b.j.join(",")}
function Dn(a,b,c){a.v=!1;Hn(b,"fatal");if(!a.g){if(b instanceof uh)throw b.g;throw ci(b);}a.g.l(b,In(a,b,c));if(a.fb){c=In(a,b,c);c.is_forceFatal=1;var d=b instanceof uh?b.g:b;Gn(a,d,c);b=ci(d);a=", context:"+JSON.stringify(In(a,d,c));b.message+=a;throw b;}}function Jn(a,b,c){a.v=!1;Hn(b,"warning");a.g&&a.g.l(b,In(a,b,c))}yn.prototype.info=function(a,b,c){this.v=c||!1;Hn(a,"incident");this.g&&this.g.l(a,In(this,a,b))};
yn.prototype.log=function(a,b,c){this.v=!!c;Hn(a,"incident");this.g&&this.g.l(a,In(this,a,b))};
function Cn(a,b,c,d,e){d=d===void 0?!0:d;if(b&&typeof b==="object"&&b.type==="error"){var f=b.error;b=JSON.stringify({error:f&&f.message?f.message:"Missing error cause.",stack:f&&f.stack?f.stack:"Missing error cause.",message:b.message,filename:b.filename,lineno:b.lineno,colno:b.colno,type:b.type});c=Error("Unhandled "+c+" with ErrorEvent: "+b)}else c=typeof b==="string"?Error("Unhandled "+c+" with: "+b):b==null?Error("Unhandled "+c+' with "null/undefined"'):b;b={};e&&(b[e]="true");d?Ma(c):a.info(c,
b)}function In(a,b,c){b instanceof uh&&(b=b.g);c=c?al(c):{};c.severity=ob(b).severity;a.V&&(c.errorGroupId=a.V);return c}
function Gn(a,b,c){var d=a.F;try{a.da(b,c)}catch(f){throw d&&!a.O&&(a.J=!1),a.F=!0,c.provideLogDataError=f.message,c.severity||(c.severity="fatal"),ci(f);}finally{if(c["severity-unprefixed"]=c.severity||"fatal",c.severity=""+c["severity-unprefixed"],!a.cb)for(var e in c)typeof c[e]==="number"||c[e]instanceof Number||typeof c[e]==="boolean"||c[e]instanceof Boolean||a.ab.includes(e)||e in c&&delete c[e]}}
yn.prototype.da=function(a,b){for(var c in this.R)try{b[c]=this.R[c](a)}catch(g){}cl(b,this.l);if((mi(),0)>0){var d=new wn,e="";li(function(g){e+=xn(d,g)});b.clientLog=e}c=b.severity||"fatal";this.kb||(c=Om(this.bb,a,c,b));this.pa&&(b.reportName=this.pa+"_"+c);b.isArrayPrototypeIntact=xm().toString();if(!("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)){try{var f=!!document.getElementById("docs-editor")}catch(g){f=!1}b.isEditorElementAttached=f.toString()}b.documentCharacterSet=document.characterSet;
f=a.stack||"";if(f.trim().length==0||f=="Not available")b["stacklessError-reportingStack"]=bi(yn.prototype.da),[a.message].concat(oa(Object.keys(b)),oa(Object.values(b))).some(function(g){return g&&g.includes("<eye3")})||(b.eye3Hint="<eye3-stackless title='Stackless JS Error - "+a.name+"'/>");this.F&&!this.O?(this.J=this.v,c=="fatal"?c="postmortem":c=="incident"&&(c="warningafterdeath")):c=="fatal"&&(this.F=!0);this.v=!1;b.severity=c};
yn.prototype.M=function(){En=!1;if(this.L)for(var a=this.L,b=v(a.et),c=b.next();!c.done;c=b.next()){c=c.value;var d=Sm(a.el,c);if(d&&(cb(d,a.lb),!d.length)){d=a.el;var e=Na(d.getAttribute("jsaction")||"");c+=":.CLIENT";e=e.replace(c+";","");e=e.replace(c,"");Wm(d,e)}}vi(this.N,this.g,this.B);bm.prototype.M.call(this)};var En=!1,Fn=null;function zn(){this.L=this.j=void 0;this.v=this.N=this.C=!1;this.g=void 0;this.F=this.l=!1;this.B=!0;this.H=[];this.U=this.o=!1;this.J=[]}
function Hn(a,b){a instanceof uh&&(a=a.g);nb(a,"severity",b)};function Kn(a,b){bm.call(this);this.da=a;this.O=b;this.L=void 0;this.status=this.readyState=0;this.responseType=this.o=this.l=this.statusText="";this.onreadystatechange=null;this.N=new Headers;this.v=null;this.V="GET";this.Z="";this.g=!1;this.R=this.B=this.F=null;this.J=new AbortController}z(Kn,bm);q=Kn.prototype;q.open=function(a,b){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.V=a;this.Z=b;this.readyState=1;Ln(this)};
q.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");if(this.J.signal.aborted)throw this.abort(),Error("Request was aborted.");this.g=!0;var b={headers:this.N,method:this.V,credentials:this.L,cache:void 0,signal:this.J.signal};a&&(b.body=a);(this.da||y).fetch(new Request(this.Z,b)).then(this.wb.bind(this),this.ua.bind(this))};
q.abort=function(){this.l=this.o="";this.N=new Headers;this.status=0;this.J.abort();this.B&&this.B.cancel("Request was aborted.").catch(k());this.readyState>=1&&this.g&&this.readyState!=4&&(this.g=!1,Mn(this));this.readyState=0};
q.wb=function(a){if(this.g&&(this.F=a,this.v||(this.status=this.F.status,this.statusText=this.F.statusText,this.v=a.headers,this.readyState=2,Ln(this)),this.g&&(this.readyState=3,Ln(this),this.g)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.tb.bind(this),this.ua.bind(this));else if(typeof y.ReadableStream!=="undefined"&&"body"in a){this.B=a.body.getReader();if(this.O){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.l=
[]}else this.l=this.o="",this.R=new TextDecoder;Nn(this)}else a.text().then(this.ub.bind(this),this.ua.bind(this))};function Nn(a){a.B.read().then(a.sb.bind(a)).catch(a.ua.bind(a))}q.sb=function(a){if(this.g){if(this.O&&a.value)this.l.push(a.value);else if(!this.O){var b=a.value?a.value:new Uint8Array(0);if(b=this.R.decode(b,{stream:!a.done}))this.l=this.o+=b}a.done?Mn(this):Ln(this);this.readyState==3&&Nn(this)}};q.ub=function(a){this.g&&(this.l=this.o=a,Mn(this))};
q.tb=function(a){this.g&&(this.l=a,Mn(this))};q.ua=function(){this.g&&Mn(this)};function Mn(a){a.readyState=4;a.F=null;a.B=null;a.R=null;Ln(a)}q.setRequestHeader=function(a,b){this.N.append(a,b)};q.getResponseHeader=function(a){return this.v?this.v.get(a.toLowerCase())||"":""};q.getAllResponseHeaders=function(){if(!this.v)return"";for(var a=[],b=this.v.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
function Ln(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(Kn.prototype,"withCredentials",{get:function(){return this.L==="include"},set:function(a){this.L=a?"include":"same-origin"}});function On(a){this.g=null;this.j=a<1;this.l=a<.01}function Pn(a,b){var c=c===void 0?{}:c;a.l&&(c.sampling_samplePercentage=(.01).toString(),a.g.info(b,c))}function Qn(a,b,c){c=c===void 0?{}:c;a.j&&(c.sampling_samplePercentage=(1).toString(),Jn(a.g,b,c))};function Rn(a){this.D=D(a)}t(Rn,F);Rn.prototype.getMessage=function(){return od(this,1)};function Sn(a){this.D=D(a)}t(Sn,F);function Tn(){var a=new Sn;return rd(a,2,Date.now().toString())};function Un(a){this.D=D(a)}t(Un,F);function Vn(a){this.D=D(a)}t(Vn,F);function Wn(a){this.D=D(a)}t(Wn,F);var Xn=ud(Wn);function Yn(a){this.D=D(a)}t(Yn,F);function Zn(a){this.D=D(a)}t(Zn,F);function $n(a,b){return Xc(a,1,b==null?b:rc(b))}Zn.prototype.Da=function(){return id(this,Rn,5)};function ao(a,b){this.j=b;this.g=a}function bo(a,b){var c=b.g;c&&c.data&&c.ports&&c.ports.length?(b=c.data?Xn(JSON.stringify(c.data)):new Wn,co(a,b,c.ports.length>1?c.ports[1]:void 0).then(function(d){c.ports[0].postMessage(Jc(d))})):Qn(a.g,Error("Dropped invalid event."),{event:String(b)})}function co(a,b,c){return Ui().then(function(){return a.j(b,c)}).za(function(d){d=d instanceof Error?d:Error(d);var e=new Zn,f=new Rn;kd(e,Rn,5,f);rd(f,1,d.message);return e})};function eo(a){this.D=D(a)}t(eo,F);function fo(a,b){return Xc(a,1,b==null?b:rc(b))}eo.prototype.Da=function(){return id(this,Rn,3)};function go(a){var b=Zi();chrome.runtime.sendMessage(Jc(a),void 0,function(c){return ho(b,function(d){return new eo(d)},c)});return b.promise.Ya(function(c){c=ai(c);nb(c,"offscreenDocumentRequestType",pd(a,1).toString());throw c;})}
function ho(a,b,c){var d=chrome.runtime;c!==void 0?(d=b(c),d.Da()?(b=a.reject,c=Error,d=d.Da(),d=qd(d,1),b.call(a,c("Error from Offscreen page:"+d))):a.resolve(d)):a.reject(Error("No response from Offscreen page:"+(d.lastError?d.lastError.message:"without lastError")))};function io(a){a=a===null?"null":a===void 0?"undefined":a;var b;Nh===void 0&&(Nh=Oh());a=(b=Nh)?b.createScriptURL(a):a;return new Ph(a)};function jo(a){Y.call(this);this.g=this.B=null;this.v=Zi();this.o=!1;this.l=0;this.F=null;this.j=new On(a)}t(jo,Y);function ko(a){y.clearTimeout(a.l);a.g&&(a.o&&(a.v=Zi(),a.o=!1),a.g.parentNode&&a.g.parentNode.removeChild(a.g),a.g=null);return Promise.resolve()}function lo(a,b){return a.g?Promise.resolve():mo(a,b)}
function mo(a,b){b||Pn(a.j,Error("Creating extension frame without an OUID."));var c=no(a,b);return ko(a).then(function(){a.g=um();a.g.id="extensionFrame";Vh(a.g,io(c));document.body.appendChild(a.g);a.l=dm(function(){Qn(a.j,Error("Timed out waiting for frame connection."));return Xi([em(),nm(a.B.B)]).then(function(){y.close()})},14E3);return Promise.resolve()})}function no(a,b){return gl(a.F,"/offline/extension/frame").toString()+"?ouid="+(b?encodeURIComponent(String(b)):"")}
function oo(a,b){return Promise.resolve(a.v.promise).then(function(c){var d=new MessageChannel;return(new Promise(function(e){d.port1.onmessage=function(f){e(new Yn(f.data))};c.postMessage(Jc(b),[d.port2])})).finally(function(){d.port1.close()})})}jo.prototype.M=function(){ko(this);Y.prototype.M.call(this)};function po(a){this.D=D(a)}t(po,F);function qo(a){this.D=D(a)}t(qo,F);function ro(a){var b=new qo;return Xc(b,1,a==null?a:rc(a))}function so(a){var b=ro(3);return kd(b,Sn,4,a)}function to(a,b){return kd(a,Vn,6,b)};function uo(){Y.call(this);var a=this;this.l=null;this.j=this.g=0;chrome.runtime.onConnectExternal.addListener(function(b){return vo(a,b)});wo(this)}t(uo,Y);function vo(a,b){a.g++;y.clearTimeout(a.j);b.onDisconnect.addListener(function(){a.g--;a.g==0&&xo(a)})}function xo(a){a.g==0&&(y.clearTimeout(a.j),a.j=dm(function(){a.g==0&&y.close()},6E4))}function wo(a){dm(function(){a.l&&Jn(a.l,ai("Force closed the offscreen document after one hour."));y.close()},36E5)};function yo(){Ha(this.l,this);this.g=new wn;this.g.j=!1;this.g.l=!1;this.j=this.g.g=!1;this.o={}}function zo(a){1!=a.j&&(a.j=!0)}yo.prototype.l=function(a){function b(f){if(f){if(f.value>=gi.value)return"error";if(f.value>=hi.value)return"warn";if(f.value>=ii.value)return"log"}return"debug"}if(!this.o[a.j()]){var c=xn(this.g,a),d=Ao;if(d){var e=b(a.o());Bo(d,e,c,a.g())}}};var Ao=y.console;function Bo(a,b,c,d){if(a[b])a[b](c,d===void 0?"":d);else a.log(c,d===void 0?"":d)};function Co(){Y.call(this);var a=this,b=new dl(self.location);this.da=Do(b,"sessionId",function(c){return String(c)},xh());this.g=this.j=this.N=null;this.l=new uo;xo(this.l);this.O=new yo;zo(this.O);this.L=new jm(this);wi(this,this.L);this.L.listen(y,"message",this.V);this.J=Do(b,"randomPercentageForSampling",function(c){return Number(c)},Math.random()*100);this.U=this.J<1;this.o=new On(this.J);this.B="unknown";this.F=null;this.v="unknown";this.R=new ao(this.o,function(c,d){return Eo(a,c,d)});chrome.runtime.onMessage.addListener(this.Z.bind(this))}
t(Co,Y);Co.prototype.V=function(a){bo(this.R,a)};function Eo(a,b,c){var d=$n(new Zn,sc(E(b,1)));switch(sc(E(b,1,void 0,Vc))){case 1:b=(b=id(b,Sn,7))?qd(b,1):null;var e=Tn();b?rd(e,1,b):Pn(a.o,Error("Scheduler frame connect request sent without an ouid."));b=so(e);return go(b).then(function(){var f=a.g;f.v.resolve(c);f.o=!0;y.clearTimeout(f.l)}).then(function(){return d});case 3:return b=to(ro(7),id(b,Vn,3)),go(b).then(function(){return d})}throw Error("Dropped unknown message "+b);}
Co.prototype.Z=function(a,b,c){var d=this;y.clearTimeout(this.l.j);var e=new qo(a);Fo(this,e).then(function(f){c(Jc(f))}).catch(function(f){var g=f instanceof Error?f:Error(f);f=new Rn;rd(f,1,g.message);g=fo(new eo,sc(E(e,1)));kd(g,Rn,3,f);c(Jc(g))}).finally(function(){xo(d.l)});return!0};
function Fo(a,b){var c=fo(new eo,sc(E(b,1)));try{switch(sc(E(b,1,void 0,Vc))){case 1:return Go(a,b),mo(a.g,a.F).then(function(){return c});case 4:return oo(a.g,id(b,Un,5)).then(function(d){kd(c,Yn,4,d);return c});case 5:return ko(a.g).then(function(){return c});case 6:return Go(a,b),lo(a.g,a.F).then(function(){return c});default:throw Error("Dropped unknown message");}}catch(d){return Promise.reject(d)}}
function Go(a,b){if(!a.g){b=id(b,po,2);a.F=qd(b,1);a.N=qd(b,2);var c;a.B=(c=yc(E(b,4)))!=null?c:a.B;var d;a.v=(d=yc(E(b,3)))!=null?d:a.v;c=new dl(a.N);var e=gl(c,"/offline/jserror").toString();d=a.U;b=a.da;var f=f===void 0?!1:f;var g=g===void 0?Dj():g;var h=new zn;h.C=!1;h.v=!0;h.g=e;h.l=f;h.j=g;h.o=!1;f=new yn(h);f.l.sessionTypeName="offline-off-screen-document";f.l.reportsNonFatalErrors=String(d);f.l.sid=b;f.l.extensionVersion=a.v;f.l.optInStatus=a.B;a.j=f;wi(a,a.j);a.o.g=a.j;a.l.l=a.j;a.g=new jo(a.J,
"OffscreenDocument");f=a.g;g=a.j;f.B=g;f.j.g=g;a.g.F=c;wi(a,a.g)}}function Do(a,b,c,d){a=a.l.ta(b);return a.length!=0?c(a[0]):d};new Co;
