/**
 * Screen Comparison Example
 * 
 * Demonstrates how to subscribe to processed video frames from the VideoFrameInterceptor
 * for real-time screen analysis and comparison.
 * 
 * This example shows:
 * - How to subscribe to frame updates
 * - Frame-to-frame difference detection
 * - Basic image analysis on video frames
 * - Performance monitoring
 */

class ScreenComparison {
  constructor(options = {}) {
    this.options = {
      debug: options.debug || false,
      diffThreshold: options.diffThreshold || 0.1, // 10% difference threshold
      analysisInterval: options.analysisInterval || 1000, // Analyze every 1000ms
      maxFrameHistory: options.maxFrameHistory || 5,
      ...options
    };

    // State management
    this.isActive = false;
    this.frameHistory = [];
    this.lastAnalysisTime = 0;
    this.frameCount = 0;
    this.significantChanges = 0;
    
    // Canvas for frame analysis
    this.canvas = null;
    this.ctx = null;
    this.previousFrameData = null;
    
    // Subscription management
    this.unsubscribe = null;
    this.interceptor = null;

    this.log('ScreenComparison initialized', this.options);
  }

  /**
   * Start screen comparison analysis
   * @param {VideoFrameInterceptor} interceptor - The frame interceptor to subscribe to
   */
  start(interceptor) {
    if (this.isActive) {
      this.log('Screen comparison already active');
      return;
    }

    if (!interceptor) {
      throw new Error('VideoFrameInterceptor instance required');
    }

    this.interceptor = interceptor;
    this.isActive = true;

    // Create canvas for frame analysis
    this.setupCanvas();

    // Subscribe to frame updates
    this.unsubscribe = interceptor.subscribe(
      'screen-comparison',
      this.handleFrame.bind(this)
    );

    this.log('Screen comparison started');
  }

  /**
   * Stop screen comparison analysis
   */
  stop() {
    if (!this.isActive) {
      this.log('Screen comparison not active');
      return;
    }

    this.isActive = false;

    // Unsubscribe from frame updates
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }

    // Clean up canvas
    this.cleanupCanvas();

    this.log('Screen comparison stopped');
  }

  /**
   * Handle incoming video frames
   * @param {VideoFrame} frame - Video frame from interceptor
   * @param {Object} metadata - Frame metadata
   */
  async handleFrame(frame, metadata) {
    try {
      this.frameCount++;
      const currentTime = performance.now();

      // Throttle analysis based on interval
      if (currentTime - this.lastAnalysisTime < this.options.analysisInterval) {
        frame.close(); // Clean up frame
        return;
      }

      this.lastAnalysisTime = currentTime;

      // Perform frame analysis
      const analysisResult = await this.analyzeFrame(frame, metadata);
      
      // Store in frame history
      this.addToHistory(analysisResult);

      // Detect significant changes
      if (this.detectSignificantChange(analysisResult)) {
        this.significantChanges++;
        this.onSignificantChange(analysisResult);
      }

      // Log periodic stats
      if (this.frameCount % 100 === 0) {
        this.logStats();
      }

      // Clean up frame
      frame.close();

    } catch (error) {
      this.log('Error handling frame:', error);
      frame.close();
    }
  }

  /**
   * Analyze a video frame
   * @param {VideoFrame} frame - Video frame to analyze
   * @param {Object} metadata - Frame metadata
   * @returns {Object} Analysis result
   */
  async analyzeFrame(frame, metadata) {
    const { codedWidth, codedHeight } = frame;
    
    // Resize canvas if needed
    if (this.canvas.width !== codedWidth || this.canvas.height !== codedHeight) {
      this.canvas.width = codedWidth;
      this.canvas.height = codedHeight;
    }

    // Draw frame to canvas
    this.ctx.drawImage(frame, 0, 0);
    
    // Get image data
    const imageData = this.ctx.getImageData(0, 0, codedWidth, codedHeight);
    const currentFrameData = imageData.data;

    // Calculate frame statistics
    const stats = this.calculateFrameStats(currentFrameData);
    
    // Calculate difference from previous frame
    let difference = 0;
    if (this.previousFrameData) {
      difference = this.calculateFrameDifference(currentFrameData, this.previousFrameData);
    }

    // Store current frame data for next comparison
    this.previousFrameData = new Uint8ClampedArray(currentFrameData);

    return {
      timestamp: metadata.timestamp,
      frameCount: metadata.frameCount,
      width: codedWidth,
      height: codedHeight,
      stats,
      difference,
      cropRegion: metadata.cropRegion
    };
  }

  /**
   * Calculate basic frame statistics
   * @param {Uint8ClampedArray} frameData - Frame pixel data
   * @returns {Object} Frame statistics
   */
  calculateFrameStats(frameData) {
    let totalBrightness = 0;
    let totalRed = 0;
    let totalGreen = 0;
    let totalBlue = 0;
    const pixelCount = frameData.length / 4;

    for (let i = 0; i < frameData.length; i += 4) {
      const r = frameData[i];
      const g = frameData[i + 1];
      const b = frameData[i + 2];
      
      totalRed += r;
      totalGreen += g;
      totalBlue += b;
      totalBrightness += (r + g + b) / 3;
    }

    return {
      avgBrightness: totalBrightness / pixelCount,
      avgRed: totalRed / pixelCount,
      avgGreen: totalGreen / pixelCount,
      avgBlue: totalBlue / pixelCount,
      pixelCount
    };
  }

  /**
   * Calculate difference between two frames
   * @param {Uint8ClampedArray} current - Current frame data
   * @param {Uint8ClampedArray} previous - Previous frame data
   * @returns {number} Difference percentage (0-1)
   */
  calculateFrameDifference(current, previous) {
    if (current.length !== previous.length) {
      return 1; // 100% different if sizes don't match
    }

    let totalDifference = 0;
    const pixelCount = current.length / 4;

    for (let i = 0; i < current.length; i += 4) {
      const rDiff = Math.abs(current[i] - previous[i]);
      const gDiff = Math.abs(current[i + 1] - previous[i + 1]);
      const bDiff = Math.abs(current[i + 2] - previous[i + 2]);
      
      // Average RGB difference for this pixel
      const pixelDiff = (rDiff + gDiff + bDiff) / 3;
      totalDifference += pixelDiff;
    }

    // Return as percentage (0-1)
    return (totalDifference / pixelCount) / 255;
  }

  /**
   * Detect if there's a significant change
   * @param {Object} analysisResult - Current frame analysis
   * @returns {boolean} True if significant change detected
   */
  detectSignificantChange(analysisResult) {
    return analysisResult.difference > this.options.diffThreshold;
  }

  /**
   * Handle significant change detection
   * @param {Object} analysisResult - Analysis result that triggered the change
   */
  onSignificantChange(analysisResult) {
    this.log('Significant change detected:', {
      difference: (analysisResult.difference * 100).toFixed(2) + '%',
      timestamp: analysisResult.timestamp,
      frameCount: analysisResult.frameCount
    });

    // Emit custom event for external listeners
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('screenchange', {
        detail: analysisResult
      }));
    }
  }

  /**
   * Add analysis result to history
   * @param {Object} result - Analysis result to store
   */
  addToHistory(result) {
    this.frameHistory.push(result);
    
    // Maintain max history size
    if (this.frameHistory.length > this.options.maxFrameHistory) {
      this.frameHistory.shift();
    }
  }

  /**
   * Setup canvas for frame analysis
   */
  setupCanvas() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    
    // Optionally add to DOM for debugging
    if (this.options.debug) {
      this.canvas.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        width: 200px;
        height: 150px;
        border: 2px solid red;
        z-index: 10000;
      `;
      document.body.appendChild(this.canvas);
    }
  }

  /**
   * Clean up canvas
   */
  cleanupCanvas() {
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }
    this.canvas = null;
    this.ctx = null;
  }

  /**
   * Get current statistics
   * @returns {Object} Current stats
   */
  getStats() {
    return {
      isActive: this.isActive,
      frameCount: this.frameCount,
      significantChanges: this.significantChanges,
      changeRate: this.frameCount > 0 ? (this.significantChanges / this.frameCount) : 0,
      historySize: this.frameHistory.length,
      lastAnalysis: this.frameHistory[this.frameHistory.length - 1] || null
    };
  }

  /**
   * Log current statistics
   */
  logStats() {
    const stats = this.getStats();
    this.log('Stats:', {
      frames: stats.frameCount,
      changes: stats.significantChanges,
      changeRate: (stats.changeRate * 100).toFixed(2) + '%'
    });
  }

  /**
   * Logging utility
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.debug) {
      console.log('[ScreenComparison]', ...args);
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ScreenComparison;
} else if (typeof window !== 'undefined') {
  window.ScreenComparison = ScreenComparison;
}

// Example usage:
/*
// Wait for frame interceptor to be available
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    // Check if frame interceptor is available
    if (window.frameInterceptor) {
      const screenComparison = new ScreenComparison({
        debug: true,
        diffThreshold: 0.05, // 5% difference threshold
        analysisInterval: 500 // Analyze every 500ms
      });

      // Start analysis
      screenComparison.start(window.frameInterceptor);

      // Listen for significant changes
      window.addEventListener('screenchange', (event) => {
        console.log('Screen change detected:', event.detail);
      });

      // Expose for manual control
      window.screenComparison = screenComparison;
    }
  });
}
*/
